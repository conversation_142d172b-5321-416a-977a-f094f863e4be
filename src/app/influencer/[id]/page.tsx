import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import { Header } from '../../../components/layout/header'
import { InfluencerProfile } from '../../../components/influencer/influencer-profile'
import { ChatInterface } from '../../../components/chat/chat-interface'
import { getInfluencer, getInfluencers } from '../../../lib/data/influencers'

interface PageProps {
  params: Promise<{
    id: string
  }>
}

// Generate static params for all influencers
export async function generateStaticParams() {
  const influencers = await getInfluencers()
  
  return influencers.map((influencer) => ({
    id: influencer.id,
  }))
}

// Generate metadata for each influencer page
export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  const { id } = await params
  const influencer = await getInfluencer(id)
  
  if (!influencer) {
    return {
      title: 'Influencer Not Found',
    }
  }

  return {
    title: `${influencer.name} | ATE Shop`,
    description: influencer.bio,
    openGraph: {
      title: `Chat with ${influencer.name}`,
      description: influencer.bio,
      images: [influencer.avatar],
    },
  }
}

export default async function InfluencerPage({ params }: PageProps) {
  const { id } = await params
  const influencer = await getInfluencer(id)
  
  if (!influencer) {
    notFound()
  }

  return (
    <div className="min-h-screen bg-background flex flex-col">
      <Header />
      
      <main className="flex-1 container mx-auto px-2 sm:px-4 py-4 sm:py-8 max-w-7xl">
        {/* Mobile-first responsive grid */}
        <div className="flex flex-col lg:grid lg:grid-cols-3 gap-4 lg:gap-8 h-full">
          {/* Influencer Profile - Collapsible on mobile, sidebar on desktop */}
          <div className="lg:col-span-1 order-1">
            <div className="sticky top-4">
              <InfluencerProfile influencer={influencer} />
            </div>
          </div>
          
          {/* Chat Interface - Full width on mobile, 2/3 on desktop */}
          <div className="lg:col-span-2 order-2 min-h-0 flex-1">
            <ChatInterface influencer={influencer} />
          </div>
        </div>
      </main>
    </div>
  )
}