// src/app/api/admin/analytics/route.ts - PROPERLY TYPED VERSION
import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin } from '../../../../lib/supabase/server'

// Define proper types for the joined data
interface ProductData {
  name: string
  brand: string
  price: number
  currency: string
}

interface InfluencerProductWithProduct {
  product_id: string
  click_count: number | null
  conversion_count: number | null
  commission_earned: number | null
  products: ProductData
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const influencerId = searchParams.get('influencer_id')
    const days = parseInt(searchParams.get('days') || '30')

    const endDate = new Date()
    const startDate = new Date(endDate.getTime() - (days * 24 * 60 * 60 * 1000))

    let query = supabaseAdmin
      .from('chat_sessions')
      .select('*')
      .gte('created_at', startDate.toISOString())
      .lte('created_at', endDate.toISOString())

    if (influencerId) {
      query = query.eq('influencer_id', influencerId)
    }

    const { data: sessions, error } = await query

    if (error) throw error

    // Get click data for this influencer
    if (influencerId) {
      const { data: rawProducts } = await supabaseAdmin
        .from('influencer_products')
        .select(`
          product_id,
          click_count,
          conversion_count,
          commission_earned,
          products (
            name,
            brand,
            price,
            currency
          )
        `)
        .eq('influencer_id', influencerId)

      // Type cast the raw data to our expected structure
      const products = rawProducts as unknown as InfluencerProductWithProduct[]

      const totalClicks = products?.reduce((sum, p) => sum + (p.click_count || 0), 0) || 0
      const totalConversions = products?.reduce((sum, p) => sum + (p.conversion_count || 0), 0) || 0
      const estimatedRevenue = products?.reduce((sum, p) => sum + (p.commission_earned || 0), 0) || 0

      const analytics = {
        totalChats: sessions?.length || 0,
        totalClicks,
        totalConversions,
        conversionRate: totalClicks > 0 ? (totalConversions / totalClicks) * 100 : 0,
        revenueGenerated: estimatedRevenue,
        avgResponseTime: sessions?.reduce((sum: number, s: any) => sum + (s.response_time_ms || 0), 0) / (sessions?.length || 1),
        totalProductsRecommended: sessions?.reduce((sum: number, s: any) => sum + (s.products_recommended?.length || 0), 0) || 0,
        avgRating: sessions?.filter((s: any) => s.user_feedback).reduce((sum: number, s: any) => sum + s.user_feedback, 0) / (sessions?.filter((s: any) => s.user_feedback).length || 1),
        topProducts: products?.sort((a, b) => (b.click_count || 0) - (a.click_count || 0)).slice(0, 5) || [],
        chatsByDay: groupChatsByDay(sessions || []),
        clicksByProduct: products?.map(p => ({
          productName: p.products?.name || 'Unknown Product',
          clicks: p.click_count || 0,
          conversions: p.conversion_count || 0
        })) || []
      }

      return NextResponse.json({ success: true, data: analytics })
    }

    // General analytics if no specific influencer
    const analytics = {
      totalChats: sessions?.length || 0,
      avgResponseTime: sessions?.reduce((sum: number, s: any) => sum + (s.response_time_ms || 0), 0) / (sessions?.length || 1),
      totalProductsRecommended: sessions?.reduce((sum: number, s: any) => sum + (s.products_recommended?.length || 0), 0) || 0,
      avgRating: sessions?.filter((s: any) => s.user_feedback).reduce((sum: number, s: any) => sum + s.user_feedback, 0) / (sessions?.filter((s: any) => s.user_feedback).length || 1),
      chatsByDay: groupChatsByDay(sessions || []),
      topInfluencers: getTopInfluencers(sessions || [])
    }

    return NextResponse.json({ success: true, data: analytics })
  } catch (error) {
    console.error('Error fetching analytics:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch analytics' },
      { status: 500 }
    )
  }
}

function groupChatsByDay(sessions: any[]) {
  const grouped = sessions.reduce((acc: any, session: any) => {
    const date = new Date(session.created_at).toISOString().split('T')[0]
    acc[date] = (acc[date] || 0) + 1
    return acc
  }, {})

  return Object.entries(grouped).map(([date, count]) => ({ date, count }))
}

function getTopInfluencers(sessions: any[]) {
  const grouped = sessions.reduce((acc: any, session: any) => {
    const id = session.influencer_id
    if (!acc[id]) {
      acc[id] = { influencer_id: id, count: 0, totalResponseTime: 0 }
    }
    acc[id].count++
    acc[id].totalResponseTime += session.response_time_ms || 0
    return acc
  }, {})

  return Object.values(grouped)
    .map((item: any) => ({
      ...item,
      avgResponseTime: item.totalResponseTime / item.count
    }))
    .sort((a: any, b: any) => b.count - a.count)
    .slice(0, 5)
}