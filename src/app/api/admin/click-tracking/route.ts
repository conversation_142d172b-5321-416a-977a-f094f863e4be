import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin } from '../../../../lib/supabase/server'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { influencer_id, product_id, type } = body // type: 'click' or 'conversion'

    if (type === 'click') {
      // Use the database function to safely increment
      const { error } = await supabaseAdmin.rpc('increment_click_count', {
        p_influencer_id: influencer_id,
        p_product_id: product_id
      })

      if (error) throw error
    } else if (type === 'conversion') {
      // Use the database function to safely increment
      const { error } = await supabaseAdmin.rpc('increment_conversion_count', {
        p_influencer_id: influencer_id,
        p_product_id: product_id,
        p_commission_amount: 0 // Can be calculated based on product price
      })

      if (error) throw error
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error tracking click/conversion:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to track action' },
      { status: 500 }
    )
  }
}
