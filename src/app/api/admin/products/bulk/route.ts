import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin } from '../../../../../lib/supabase/server'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { products } = body

    if (!Array.isArray(products) || products.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Products array is required' },
        { status: 400 }
      )
    }

    const { data, error } = await supabaseAdmin
      .from('products')
      .insert(products)
      .select()

    if (error) throw error

    return NextResponse.json({ 
      success: true, 
      data, 
      message: `Successfully imported ${data.length} products` 
    })
  } catch (error) {
    console.error('Error bulk importing products:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to import products' },
      { status: 500 }
    )
  }
}

