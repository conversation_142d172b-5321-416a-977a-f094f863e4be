import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin } from '../../../../lib/supabase/server'

export async function POST(request: NextRequest) {
  try {
    const data = await request.formData()
    const file: File | null = data.get('file') as unknown as File
    const type: string = data.get('type') as string // 'avatar' or 'cover'
    const influencerId: string = data.get('influencerId') as string

    if (!file) {
      return NextResponse.json({ success: false, error: 'No file received' }, { status: 400 })
    }

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json(
        { success: false, error: 'Invalid file type. Only JPEG, PNG, and WebP are allowed.' },
        { status: 400 }
      )
    }

    // Validate file size (5MB max)
    if (file.size > 5 * 1024 * 1024) {
      return NextResponse.json(
        { success: false, error: 'File too large. Maximum size is 5MB.' },
        { status: 400 }
      )
    }

    // Create filename
    const timestamp = Date.now()
    const extension = file.name.split('.').pop()
    const filename = `${influencerId}-${type}-${timestamp}.${extension}`
    const filepath = `influencers/${filename}`

    // Convert file to buffer
    const bytes = await file.arrayBuffer()
    const buffer = Buffer.from(bytes)

    // Upload to Supabase Storage
    const { data: uploadData, error: uploadError } = await supabaseAdmin.storage
      .from('ate-shop-images') // Create this bucket in Supabase
      .upload(filepath, buffer, {
        contentType: file.type,
        upsert: true // Replace if exists
      })

    if (uploadError) {
      console.error('Supabase upload error:', uploadError)
      return NextResponse.json(
        { success: false, error: 'Failed to upload to storage: ' + uploadError.message },
        { status: 500 }
      )
    }

    // Get public URL
    const { data: publicUrlData } = supabaseAdmin.storage
      .from('ate-shop-images')
      .getPublicUrl(filepath)

    const publicUrl = publicUrlData.publicUrl

    return NextResponse.json({ 
      success: true, 
      url: publicUrl,
      filename,
      path: filepath
    })

  } catch (error) {
    console.error('Upload error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to upload file' },
      { status: 500 }
    )
  }
}

// Optional: DELETE endpoint to remove old images
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const filepath = searchParams.get('path')

    if (!filepath) {
      return NextResponse.json({ success: false, error: 'No file path provided' }, { status: 400 })
    }

    const { error } = await supabaseAdmin.storage
      .from('ate-shop-images')
      .remove([filepath])

    if (error) {
      console.error('Supabase delete error:', error)
      return NextResponse.json(
        { success: false, error: 'Failed to delete file' },
        { status: 500 }
      )
    }

    return NextResponse.json({ success: true })

  } catch (error) {
    console.error('Delete error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to delete file' },
      { status: 500 }
    )
  }
}
