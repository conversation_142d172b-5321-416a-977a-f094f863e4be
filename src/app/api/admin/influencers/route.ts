import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY! // Use service role for admin operations
)

// GET /api/admin/influencers - List all influencers
export async function GET() {
  try {
    const { data, error } = await supabase
      .from('influencers')
      .select('*')
      .order('created_at', { ascending: false })

    if (error) throw error

    return NextResponse.json({ success: true, data })
  } catch (error) {
    console.error('Error fetching influencers:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch influencers' },
      { status: 500 }
    )
  }
}

// POST /api/admin/influencers - Create new influencer
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      id,
      name,
      username,
      avatar,
      cover_image,
      bio,
      specialty,
      followers,
      categories,
      style_keywords,
      social_links,
      verified,
      personality_prompt
    } = body

    const { data, error } = await supabase
      .from('influencers')
      .insert({
        id,
        name,
        username,
        avatar,
        cover_image,
        bio,
        specialty,
        followers,
        categories,
        style_keywords,
        social_links,
        verified,
        personality_prompt
      })
      .select()
      .single()

    if (error) throw error

    return NextResponse.json({ success: true, data })
  } catch (error) {
    console.error('Error creating influencer:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to create influencer' },
      { status: 500 }
    )
  }
}