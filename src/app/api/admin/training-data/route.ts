// src/app/api/admin/training-data/route.ts - Cleaned and simplified
import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin } from '../../../../lib/supabase/server'
import OpenAI from 'openai'

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
})

// GET /api/admin/training-data - Get training data for an influencer
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const influencerId = searchParams.get('influencer_id')

    if (!influencerId) {
      return NextResponse.json(
        { success: false, error: 'Influencer ID is required' },
        { status: 400 }
      )
    }

    const { data, error } = await supabaseAdmin
      .from('ai_training_data')
      .select('*')
      .eq('influencer_id', influencerId)
      .eq('is_active', true)
      .order('created_at', { ascending: false })

    if (error) throw error

    // Calculate analytics
    const analytics = {
      totalExamples: data?.length || 0,
      categories: {
        product_recommendation: data?.filter(d => d.category === 'product_recommendation').length || 0,
        style_advice: data?.filter(d => d.category === 'style_advice').length || 0,
        general_chat: data?.filter(d => d.category === 'general_chat').length || 0,
        personality: data?.filter(d => d.category === 'personality').length || 0,
        brand_voice: data?.filter(d => d.category === 'brand_voice').length || 0,
      },
      lastUpdated: data?.[0]?.created_at || null
    }

    return NextResponse.json({ 
      success: true, 
      data: data || [],
      analytics 
    })

  } catch (error) {
    console.error('Error fetching training data:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch training data' },
      { status: 500 }
    )
  }
}

// POST /api/admin/training-data - Add new training data
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      influencer_id,
      category,
      user_input,
      ai_response,
      context_tags
    } = body

    // Validate required fields
    if (!influencer_id || !category || !user_input || !ai_response) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Validate and enhance the training data
    const validation = await validateTrainingData(user_input, ai_response, influencer_id)

    const { data, error } = await supabaseAdmin
      .from('ai_training_data')
      .insert({
        influencer_id,
        category,
        user_input,
        ai_response,
        context_tags: context_tags || [],
        is_active: validation.isValid,
        created_at: new Date().toISOString()
      })
      .select()
      .single()

    if (error) throw error

    return NextResponse.json({ 
      success: true, 
      data,
      validation 
    })

  } catch (error) {
    console.error('Error creating training data:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to create training data' },
      { status: 500 }
    )
  }
}

// Helper function to validate training data
async function validateTrainingData(userInput: string, aiResponse: string, influencerId: string) {
  try {
    // Get influencer info for validation
    const { data: influencer } = await supabaseAdmin
      .from('influencers')
      .select('name, personality_prompt')
      .eq('id', influencerId)
      .single()

    if (!influencer) {
      return { isValid: false, score: 0, issues: ['Influencer not found'] }
    }

    // Use AI to validate the training data quality
    const validationPrompt = `
Validate this training data for ${influencer.name}:

User Input: "${userInput}"
AI Response: "${aiResponse}"

Rate this training data (1-10) based on:
1. Response quality and helpfulness
2. Personality consistency 
3. Natural conversation flow
4. Appropriate tone and style

Return only a JSON object:
{
  "score": number,
  "isValid": boolean,
  "issues": ["list", "of", "issues"]
}
`

    const response = await openai.chat.completions.create({
      model: "gpt-4o-mini",
      messages: [{ role: "user", content: validationPrompt }],
      temperature: 0.1,
      max_tokens: 200
    })

    const validation = JSON.parse(response.choices[0]?.message?.content || '{}')
    
    return {
      isValid: validation.score >= 7,
      score: validation.score || 0,
      issues: validation.issues || []
    }

  } catch (error) {
    console.error('Validation error:', error)
    return { isValid: true, score: 8, issues: [] } // Default to valid if validation fails
  }
}

// Advanced training operations
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { action, influencer_id, data } = body

    switch (action) {
      case 'generate_from_logs':
        return await generateFromLogs(influencer_id, data)
      
      case 'optimize_prompt':
        return await optimizePrompt(influencer_id)
      
      case 'bulk_import':
        return await bulkImport(influencer_id, data)
      
      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action' },
          { status: 400 }
        )
    }

  } catch (error) {
    console.error('Error in training PUT operation:', error)
    return NextResponse.json(
      { success: false, error: 'Operation failed' },
      { status: 500 }
    )
  }
}

// Generate training data from chat logs
async function generateFromLogs(influencerId: string, options: any) {
  try {
    const days = options?.days || 30

    // Get recent chat sessions
    const { data: logs, error } = await supabaseAdmin
      .from('chat_sessions')
      .select('*')
      .eq('influencer_id', influencerId)
      .gte('created_at', new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString())
      .order('created_at', { ascending: false })
      .limit(50)

    if (error) throw error

    const trainingData = []

    for (const log of logs) {
      // Validate quality
      const validation = await validateTrainingData(log.user_message, log.ai_response, influencerId)
      
      if (validation.score >= 7) {
        trainingData.push({
          influencer_id: influencerId,
          category: categorizeConversation(log.user_message),
          user_input: log.user_message,
          ai_response: log.ai_response,
          context_tags: extractContextTags(log.user_message),
          is_active: true
        })
      }
    }

    // Save high-quality data
    if (trainingData.length > 0) {
      const { error: insertError } = await supabaseAdmin
        .from('ai_training_data')
        .insert(trainingData)

      if (insertError) throw insertError
    }

    return NextResponse.json({
      success: true,
      data: {
        processed: logs.length,
        saved: trainingData.length,
        message: `Generated ${trainingData.length} training examples from ${logs.length} chat logs`
      }
    })

  } catch (error) {
    console.error('Error generating from logs:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to generate training data from logs' },
      { status: 500 }
    )
  }
}

// Optimize personality prompt using existing training data
async function optimizePrompt(influencerId: string) {
  try {
    // Get existing training data
    const { data: trainingData } = await supabaseAdmin
      .from('ai_training_data')
      .select('*')
      .eq('influencer_id', influencerId)
      .eq('is_active', true)
      .order('created_at', { ascending: false })
      .limit(20)

    if (!trainingData || trainingData.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'No training data available for optimization'
      })
    }

    // Get current influencer data
    const { data: influencer } = await supabaseAdmin
      .from('influencers')
      .select('*')
      .eq('id', influencerId)
      .single()

    if (!influencer) {
      return NextResponse.json({
        success: false,
        error: 'Influencer not found'
      })
    }

    // Generate optimized prompt
    const optimizationPrompt = `
Analyze this influencer's training data and create an optimized personality prompt:

CURRENT INFLUENCER: ${influencer.name}
SPECIALTY: ${influencer.specialty}
CURRENT PROMPT: ${influencer.personality_prompt || 'None'}

TRAINING EXAMPLES:
${trainingData.slice(0, 10).map(d => `
User: "${d.user_input}"
AI: "${d.ai_response}"
`).join('\n')}

Create an improved personality prompt that:
1. Captures the authentic voice and style
2. Ensures consistent personality traits
3. Includes specific response patterns
4. Maintains brand alignment
5. Improves response quality

Return only the optimized prompt text (no additional formatting):
`

    const response = await openai.chat.completions.create({
      model: "gpt-4o",
      messages: [{ role: "user", content: optimizationPrompt }],
      temperature: 0.3,
      max_tokens: 1000
    })

    const optimizedPrompt = response.choices[0]?.message?.content

    if (!optimizedPrompt) {
      throw new Error('Failed to generate optimized prompt')
    }

    // Update the influencer's prompt
    const { error: updateError } = await supabaseAdmin
      .from('influencers')
      .update({ 
        personality_prompt: optimizedPrompt,
        updated_at: new Date().toISOString()
      })
      .eq('id', influencerId)

    if (updateError) throw updateError

    return NextResponse.json({
      success: true,
      data: {
        optimizedPrompt,
        message: 'Personality prompt optimized and updated'
      }
    })

  } catch (error) {
    console.error('Error optimizing prompt:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to optimize prompt' },
      { status: 500 }
    )
  }
}

// Bulk import training data
async function bulkImport(influencerId: string, importData: any[]) {
  try {
    const results = { success: 0, failed: 0, errors: [] as string[] }

    for (const item of importData) {
      try {
        // Validate each item
        if (!item.user_input || !item.ai_response) {
          results.failed++
          results.errors.push('Missing required fields')
          continue
        }

        const validation = await validateTrainingData(item.user_input, item.ai_response, influencerId)
        
        const trainingPoint = {
          influencer_id: influencerId,
          category: item.category || categorizeConversation(item.user_input),
          user_input: item.user_input,
          ai_response: item.ai_response,
          context_tags: item.context_tags || extractContextTags(item.user_input),
          is_active: validation.isValid
        }

        const { error } = await supabaseAdmin
          .from('ai_training_data')
          .insert(trainingPoint)

        if (error) throw error

        results.success++

      } catch (error) {
        results.failed++
        results.errors.push(error instanceof Error ? error.message : 'Unknown error')
      }
    }

    return NextResponse.json({
      success: true,
      data: results
    })

  } catch (error) {
    console.error('Error in bulk import:', error)
    return NextResponse.json(
      { success: false, error: 'Bulk import failed' },
      { status: 500 }
    )
  }
}

// Helper functions
function categorizeConversation(userInput: string): string {
  const input = userInput.toLowerCase()
  
  if (input.includes('recommend') || input.includes('suggest')) {
    return 'product_recommendation'
  }
  if (input.includes('style') || input.includes('outfit')) {
    return 'style_advice'
  }
  if (input.includes('brand') || input.includes('company')) {
    return 'brand_voice'
  }
  
  return 'general_chat'
}

function extractContextTags(userInput: string): string[] {
  const tags: string[] = []
  const input = userInput.toLowerCase()
  
  // Common context tags
  const contextMap = {
    'casual': ['casual', 'everyday', 'comfortable'],
    'formal': ['formal', 'dressy', 'elegant'],
    'work': ['work', 'office', 'professional'],
    'date': ['date', 'romantic', 'dinner'],
    'party': ['party', 'night out', 'club'],
    'beach': ['beach', 'swim', 'vacation'],
    'summer': ['summer', 'hot', 'warm'],
    'winter': ['winter', 'cold', 'warm'],
    'budget': ['cheap', 'affordable', 'budget'],
    'luxury': ['expensive', 'luxury', 'high-end']
  }

  Object.entries(contextMap).forEach(([tag, keywords]) => {
    if (keywords.some(keyword => input.includes(keyword))) {
      tags.push(tag)
    }
  })

  return tags
}