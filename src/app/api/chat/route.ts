// src/app/api/chat/route.ts
import { NextRequest, NextResponse } from 'next/server'
import { simpleChatService } from '../../../lib/services/simple-chat-service'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { message, influencerId, conversationId, sessionId } = body

    if (!message || !influencerId) {
      return NextResponse.json(
        { 
          success: false, 
          error: { message: 'Message and influencerId are required' }
        },
        { status: 400 }
      )
    }

    const response = await simpleChatService.processMessage({
      message,
      influencerId,
      conversationId,
      sessionId
    })

    return NextResponse.json({
      success: true,
      data: response
    })
  } catch (error: unknown) {
    console.error('Chat API error:', error)
    const errorMessage = error instanceof Error ? error.message : 'Internal server error'
    
    return NextResponse.json(
      { 
        success: false, 
        error: { message: errorMessage }
      },
      { status: 500 }
    )
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  })
}