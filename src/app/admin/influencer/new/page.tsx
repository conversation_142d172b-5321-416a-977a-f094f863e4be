"use client"

import { useState } from 'react'
import { use<PERSON><PERSON><PERSON> } from 'next/navigation'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { ArrowLeft, Save } from 'lucide-react'

export default function NewInfluencerPage() {
  const router = useRouter()
  const [saving, setSaving] = useState(false)
  const [formData, setFormData] = useState({
    id: '',
    name: '',
    username: '',
    specialty: '',
    followers: '',
    bio: '',
    categories: '',
    style_keywords: '',
    social_links: {
      instagram: '',
      tiktok: '',
      youtube: ''
    },
    personality_prompt: ''
  })

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setSaving(true)

    try {
      const payload = {
        ...formData,
        categories: formData.categories.split(',').map(c => c.trim()).filter(Boolean),
        style_keywords: formData.style_keywords.split(',').map(k => k.trim()).filter(Boolean),
        verified: false,
        is_active: false // Start as draft
      }

      const response = await fetch('/api/admin/influencers', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload)
      })

      const data = await response.json()
      if (data.success) {
        router.push(`/admin/influencer/${data.data.id}`)
      } else {
        alert('Error creating influencer: ' + data.error)
      }
    } catch (error) {
      console.error('Error creating influencer:', error)
      alert('Error creating influencer')
    } finally {
      setSaving(false)
    }
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="border-b">
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center gap-4">
            <Button variant="ghost" onClick={() => router.back()}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
            <div>
              <h1 className="text-3xl font-bold">Add New Influencer</h1>
              <p className="text-muted-foreground">Create a new AI-powered influencer profile</p>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8 max-w-2xl">
        <form onSubmit={handleSubmit} className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Basic Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium">Influencer ID</label>
                <Input
                  required
                  value={formData.id}
                  onChange={(e) => setFormData({...formData, id: e.target.value})}
                  placeholder="alix-earle"
                />
                <p className="text-xs text-muted-foreground">
                  Unique identifier for URL (lowercase, hyphens only)
                </p>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium">Full Name</label>
                  <Input
                    required
                    value={formData.name}
                    onChange={(e) => setFormData({...formData, name: e.target.value})}
                    placeholder="Alix Earle"
                  />
                </div>
                <div>
                  <label className="text-sm font-medium">Username</label>
                  <Input
                    required
                    value={formData.username}
                    onChange={(e) => setFormData({...formData, username: e.target.value})}
                    placeholder="alixearle"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium">Specialty</label>
                  <Input
                    required
                    value={formData.specialty}
                    onChange={(e) => setFormData({...formData, specialty: e.target.value})}
                    placeholder="Fashion & Lifestyle"
                  />
                </div>
                <div>
                  <label className="text-sm font-medium">Followers</label>
                  <Input
                    required
                    value={formData.followers}
                    onChange={(e) => setFormData({...formData, followers: e.target.value})}
                    placeholder="5.2M"
                  />
                </div>
              </div>

              <div>
                <label className="text-sm font-medium">Bio</label>
                <textarea
                  className="w-full p-2 border rounded"
                  rows={3}
                  value={formData.bio}
                  onChange={(e) => setFormData({...formData, bio: e.target.value})}
                  placeholder="Miami-based lifestyle influencer..."
                />
              </div>

              <div>
                <label className="text-sm font-medium">Categories (comma separated)</label>
                <Input
                  value={formData.categories}
                  onChange={(e) => setFormData({...formData, categories: e.target.value})}
                  placeholder="Fashion, Beauty, Lifestyle"
                />
              </div>

              <div>
                <label className="text-sm font-medium">Style Keywords (comma separated)</label>
                <Input
                  value={formData.style_keywords}
                  onChange={(e) => setFormData({...formData, style_keywords: e.target.value})}
                  placeholder="trendy, chic, casual, elegant"
                />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Social Media Links</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium">Instagram</label>
                <Input
                  value={formData.social_links.instagram}
                  onChange={(e) => setFormData({
                    ...formData, 
                    social_links: {...formData.social_links, instagram: e.target.value}
                  })}
                  placeholder="https://instagram.com/username"
                />
              </div>
              <div>
                <label className="text-sm font-medium">TikTok</label>
                <Input
                  value={formData.social_links.tiktok}
                  onChange={(e) => setFormData({
                    ...formData, 
                    social_links: {...formData.social_links, tiktok: e.target.value}
                  })}
                  placeholder="https://tiktok.com/@username"
                />
              </div>
              <div>
                <label className="text-sm font-medium">YouTube</label>
                <Input
                  value={formData.social_links.youtube}
                  onChange={(e) => setFormData({
                    ...formData, 
                    social_links: {...formData.social_links, youtube: e.target.value}
                  })}
                  placeholder="https://youtube.com/@username"
                />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>AI Personality</CardTitle>
            </CardHeader>
            <CardContent>
              <div>
                <label className="text-sm font-medium">Personality Prompt</label>
                <textarea
                  className="w-full p-3 border rounded font-mono text-sm"
                  rows={6}
                  value={formData.personality_prompt}
                  onChange={(e) => setFormData({...formData, personality_prompt: e.target.value})}
                  placeholder={`You are ${formData.name || '[Name]'}, the ${formData.specialty || '[specialty]'} influencer...`}
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Define how the AI should respond as this influencer
                </p>
              </div>
            </CardContent>
          </Card>

          <div className="flex justify-end space-x-2">
            <Button type="button" variant="outline" onClick={() => router.back()}>
              Cancel
            </Button>
            <Button type="submit" disabled={saving}>
              {saving ? 'Creating...' : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Create Influencer
                </>
              )}
            </Button>
          </div>
        </form>
      </div>
    </div>
  )
}