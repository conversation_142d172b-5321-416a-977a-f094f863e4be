// src/app/admin/influencer/[id]/page.tsx - Individual Influencer Management Dashboard
"use client"

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { 
  Plus, Settings, Upload, BarChart3, MessageCircle, Edit, 
  Brain, Target, Zap, AlertTriangle, CheckCircle, Clock,
  ExternalLink, Trash2, Copy, Eye, TrendingUp, Users,
  ShoppingBag, Star, Play, Pause, Save
} from 'lucide-react'

// Import components
import { ImageUpload } from '@/components/admin/ImageUpload'

interface Influencer {
  id: string
  name: string
  username: string
  specialty: string
  followers: string
  verified: boolean
  is_active: boolean
  total_recommendations: number
  avatar?: string
  cover_image?: string
  bio?: string
  personality_prompt?: string
  categories?: string[]
  style_keywords?: string[]
  social_links?: any
}

interface Product {
  id: string
  name: string
  brand: string
  category: string
  subcategory?: string
  description?: string
  price: number
  currency: string
  image_url: string
  custom_description?: string
  affiliate_url?: string
  recommendation_priority: number
  is_active: boolean
  click_count: number
  conversion_count: number
  season_tags?: string[]
  style_tags?: string[]
  color_tags?: string[]
}

interface Analytics {
  totalChats: number
  totalClicks: number
  conversionRate: number
  revenueGenerated: number
  topProducts: Product[]
  recentActivity: any[]
}

export default function InfluencerManagementDashboard() {
  const params = useParams()
  const router = useRouter()
  const influencerId = params.id as string
  
  const [influencer, setInfluencer] = useState<Influencer | null>(null)
  const [products, setProducts] = useState<Product[]>([])
  const [analytics, setAnalytics] = useState<Analytics | null>(null)
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState('overview')
  
  // Form states
  const [showProductForm, setShowProductForm] = useState(false)
  const [editingProduct, setEditingProduct] = useState<Product | null>(null)
  const [showPromptEditor, setShowPromptEditor] = useState(false)

  useEffect(() => {
    if (influencerId) {
      loadInfluencerData()
    }
  }, [influencerId])

  const loadInfluencerData = async () => {
    try {
      // Load influencer details
      const influencerRes = await fetch(`/api/admin/influencers/${influencerId}`)
      const influencerData = await influencerRes.json()
      
      if (influencerData.success) {
        setInfluencer(influencerData.data)
      }

      // Load products
      const productsRes = await fetch(`/api/admin/influencer-products?influencer_id=${influencerId}`)
      const productsData = await productsRes.json()
      
      if (productsData.success) {
        setProducts(productsData.data.map((item: any) => ({
          id: item.product_id,
          name: item.products.name,
          brand: item.products.brand,
          category: item.products.category,
          subcategory: item.products.subcategory,
          description: item.products.description,
          price: item.products.price,
          currency: item.products.currency,
          image_url: item.products.image_url,
          custom_description: item.custom_description,
          affiliate_url: item.affiliate_url,
          recommendation_priority: item.recommendation_priority,
          is_active: item.is_active,
          click_count: item.click_count || 0,
          conversion_count: item.conversion_count || 0,
          season_tags: item.products.season_tags,
          style_tags: item.products.style_tags,
          color_tags: item.products.color_tags
        })))
      }

      // Load analytics
      const analyticsRes = await fetch(`/api/admin/analytics?influencer_id=${influencerId}&days=30`)
      const analyticsData = await analyticsRes.json()
      
      if (analyticsData.success) {
        setAnalytics(analyticsData.data)
      }

    } catch (error) {
      console.error('Error loading influencer data:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleToggleInfluencerStatus = async () => {
    if (!influencer) return
    
    try {
      const response = await fetch(`/api/admin/influencers/${influencerId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          is_active: !influencer.is_active
        })
      })

      if (response.ok) {
        setInfluencer(prev => prev ? { ...prev, is_active: !prev.is_active } : null)
      }
    } catch (error) {
      console.error('Error toggling influencer status:', error)
    }
  }

  const handleAddProduct = () => {
    setEditingProduct(null)
    setShowProductForm(true)
  }

  const handleEditProduct = (product: Product) => {
    setEditingProduct(product)
    setShowProductForm(true)
  }

  const handleDeleteProduct = async (productId: string) => {
    if (!confirm('Are you sure you want to remove this product?')) return
    
    try {
      const response = await fetch(`/api/admin/influencer-products/${productId}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        setProducts(prev => prev.filter(p => p.id !== productId))
      }
    } catch (error) {
      console.error('Error deleting product:', error)
    }
  }

  const handleSavePersonalityPrompt = async (prompt: string) => {
    if (!influencer) return
    
    try {
      const response = await fetch(`/api/admin/influencers/${influencerId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          personality_prompt: prompt
        })
      })

      if (response.ok) {
        setInfluencer(prev => prev ? { ...prev, personality_prompt: prompt } : null)
        setShowPromptEditor(false)
      }
    } catch (error) {
      console.error('Error saving personality prompt:', error)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin mx-auto mb-4" />
          <p className="text-muted-foreground">Loading influencer dashboard...</p>
        </div>
      </div>
    )
  }

  if (!influencer) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Influencer Not Found</h1>
          <Button onClick={() => router.push('/admin')}>
            Back to Admin
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="border-b bg-muted/30">
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <img
                src={influencer.avatar || `https://ui-avatars.com/api/?name=${encodeURIComponent(influencer.name)}&size=80`}
                alt={influencer.name}
                className="w-16 h-16 rounded-full border-2 border-background shadow-lg"
              />
              <div>
                <h1 className="text-3xl font-bold">{influencer.name}</h1>
                <p className="text-muted-foreground">@{influencer.username} • {influencer.specialty}</p>
                <div className="flex items-center gap-2 mt-1">
                  <Badge variant={influencer.is_active ? "default" : "secondary"}>
                    {influencer.is_active ? "Live" : "Draft"}
                  </Badge>
                  {influencer.verified && (
                    <Badge variant="outline">✓ Verified</Badge>
                  )}
                  <span className="text-sm text-muted-foreground">{influencer.followers} followers</span>
                </div>
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              <Button variant="outline" asChild>
                <a href={`/influencer/${influencer.id}`} target="_blank" rel="noopener noreferrer">
                  <Eye className="h-4 w-4 mr-2" />
                  View Live
                </a>
              </Button>
              
              <Button
                variant={influencer.is_active ? "secondary" : "default"}
                onClick={handleToggleInfluencerStatus}
              >
                {influencer.is_active ? (
                  <>
                    <Pause className="h-4 w-4 mr-2" />
                    Take Offline
                  </>
                ) : (
                  <>
                    <Play className="h-4 w-4 mr-2" />
                    Go Live
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="products">Products</TabsTrigger>
            <TabsTrigger value="ai-training">AI & Training</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
            <TabsTrigger value="settings">Settings</TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            {/* Quick Stats */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Products</CardTitle>
                  <ShoppingBag className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{products.length}</div>
                  <p className="text-xs text-muted-foreground">
                    {products.filter(p => p.is_active).length} active
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Chats</CardTitle>
                  <MessageCircle className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{analytics?.totalChats || 0}</div>
                  <p className="text-xs text-muted-foreground">Last 30 days</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Click Rate</CardTitle>
                  <TrendingUp className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {analytics?.totalClicks ? 
                      `${((analytics.totalClicks / analytics.totalChats) * 100).toFixed(1)}%` : 
                      '0%'
                    }
                  </div>
                  <p className="text-xs text-muted-foreground">Avg per conversation</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Revenue</CardTitle>
                  <Star className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    ${analytics?.revenueGenerated?.toLocaleString() || '0'}
                  </div>
                  <p className="text-xs text-muted-foreground">Estimated monthly</p>
                </CardContent>
              </Card>
            </div>

            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Button onClick={handleAddProduct} className="h-20 flex-col">
                  <Plus className="h-8 w-8 mb-2" />
                  Add Product
                </Button>
                <Button onClick={() => setShowPromptEditor(true)} variant="outline" className="h-20 flex-col">
                  <Brain className="h-8 w-8 mb-2" />
                  Edit AI Personality
                </Button>
                <Button variant="outline" className="h-20 flex-col">
                  <BarChart3 className="h-8 w-8 mb-2" />
                  View Analytics
                </Button>
              </CardContent>
            </Card>

            {/* Top Products */}
            <Card>
              <CardHeader>
                <CardTitle>Top Performing Products</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {products
                    .sort((a, b) => (b.click_count || 0) - (a.click_count || 0))
                    .slice(0, 3)
                    .map((product) => (
                    <div key={product.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-4">
                        <img 
                          src={product.image_url} 
                          alt={product.name}
                          className="w-12 h-12 object-cover rounded"
                        />
                        <div>
                          <h4 className="font-medium">{product.name}</h4>
                          <p className="text-sm text-muted-foreground">{product.brand}</p>
                        </div>
                      </div>
                      <div className="flex gap-6 text-sm">
                        <div className="text-center">
                          <p className="font-medium">{product.click_count || 0}</p>
                          <p className="text-muted-foreground">Clicks</p>
                        </div>
                        <div className="text-center">
                          <p className="font-medium">{product.conversion_count || 0}</p>
                          <p className="text-muted-foreground">Sales</p>
                        </div>
                        <div className="text-center">
                          <p className="font-medium">
                            {product.click_count ? 
                              `${((product.conversion_count || 0) / product.click_count * 100).toFixed(1)}%` : 
                              '0%'
                            }
                          </p>
                          <p className="text-muted-foreground">Conv Rate</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Products Tab */}
          <TabsContent value="products" className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-2xl font-bold">Product Catalog</h2>
              <Button onClick={handleAddProduct}>
                <Plus className="h-4 w-4 mr-2" />
                Add Product
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {products.map((product) => (
                <Card key={product.id}>
                  <CardContent className="p-4">
                    <div className="relative mb-3">
                      <img 
                        src={product.image_url} 
                        alt={product.name}
                        className="w-full h-32 object-cover rounded"
                      />
                      <Badge 
                        className="absolute top-2 right-2"
                        variant={product.is_active ? "default" : "secondary"}
                      >
                        {product.is_active ? "Active" : "Inactive"}
                      </Badge>
                      {product.recommendation_priority >= 8 && (
                        <Badge className="absolute top-2 left-2 bg-primary/90">
                          High Priority
                        </Badge>
                      )}
                    </div>
                    
                    <div className="space-y-2">
                      <h3 className="font-semibold line-clamp-2">{product.name}</h3>
                      <p className="text-sm text-muted-foreground">{product.brand}</p>
                      <p className="font-bold text-primary">
                        ${product.price} {product.currency}
                      </p>
                      
                      {product.custom_description && (
                        <p className="text-xs text-muted-foreground line-clamp-2 italic">
                          "{product.custom_description}"
                        </p>
                      )}
                      
                      <div className="flex items-center justify-between pt-2">
                        <div className="flex gap-4 text-xs">
                          <span>{product.click_count || 0} clicks</span>
                          <span>{product.conversion_count || 0} sales</span>
                        </div>
                        
                        <div className="flex items-center gap-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEditProduct(product)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteProduct(product.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* AI Training Tab */}
          <TabsContent value="ai-training" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Brain className="h-5 w-5" />
                    AI Personality
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <p className="text-sm text-muted-foreground">
                      Current personality prompt defines how the AI responds as {influencer.name}.
                    </p>
                    <Button 
                      variant="outline" 
                      onClick={() => setShowPromptEditor(true)}
                      className="w-full"
                    >
                      Edit Personality
                    </Button>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Target className="h-5 w-5" />
                    Training Data
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold">0</div>
                    <p className="text-sm text-muted-foreground">Training examples</p>
                  </div>
                  <Button variant="outline" className="w-full">
                    Import from Chats
                  </Button>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <CheckCircle className="h-5 w-5" />
                    Performance
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm">Response Quality</span>
                      <span className="text-sm font-medium">85%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">Brand Alignment</span>
                      <span className="text-sm font-medium">92%</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Personality Editor Modal */}
            {showPromptEditor && (
              <PersonalityEditor
                influencer={influencer}
                onSave={handleSavePersonalityPrompt}
                onClose={() => setShowPromptEditor(false)}
              />
            )}
          </TabsContent>

          {/* Analytics Tab */}
          <TabsContent value="analytics" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Total Conversations</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{analytics?.totalChats || 0}</div>
                  <p className="text-xs text-muted-foreground">Last 30 days</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Product Clicks</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{analytics?.totalClicks || 0}</div>
                  <p className="text-xs text-muted-foreground">Total affiliate clicks</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Conversion Rate</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {analytics?.conversionRate ? `${analytics.conversionRate.toFixed(1)}%` : '0%'}
                  </div>
                  <p className="text-xs text-muted-foreground">Click to purchase</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Est. Revenue</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    ${analytics?.revenueGenerated?.toLocaleString() || '0'}
                  </div>
                  <p className="text-xs text-muted-foreground">Commission earned</p>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Settings Tab */}
          <TabsContent value="settings" className="space-y-6">
            <InfluencerSettingsForm influencer={influencer} onUpdate={setInfluencer} />
          </TabsContent>
        </Tabs>
      </div>

      {/* Product Form Modal */}
      {showProductForm && (
        <ProductFormModal
          influencerId={influencerId}
          product={editingProduct}
          onClose={() => {
            setShowProductForm(false)
            setEditingProduct(null)
          }}
          onSave={() => {
            loadInfluencerData()
            setShowProductForm(false)
            setEditingProduct(null)
          }}
        />
      )}
    </div>
  )
}

// Component for editing personality prompt
function PersonalityEditor({ 
  influencer, 
  onSave, 
  onClose 
}: { 
  influencer: Influencer
  onSave: (prompt: string) => void
  onClose: () => void 
}) {
  const [prompt, setPrompt] = useState(influencer.personality_prompt || '')

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
      <Card className="max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <CardHeader>
          <CardTitle>AI Personality Prompt for {influencer.name}</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <label className="text-sm font-medium">Personality Prompt</label>
            <textarea
              className="w-full h-64 p-3 border rounded font-mono text-sm"
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              placeholder={`You are ${influencer.name}, the ${influencer.specialty} influencer...`}
            />
            <p className="text-xs text-muted-foreground mt-1">
              This defines how the AI responds as {influencer.name}. Be specific about personality, tone, and style.
            </p>
          </div>

          <div className="flex justify-end space-x-2">
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button onClick={() => onSave(prompt)}>
              <Save className="h-4 w-4 mr-2" />
              Save Personality
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

// Component for product form
function ProductFormModal({ 
  influencerId, 
  product, 
  onClose, 
  onSave 
}: { 
  influencerId: string
  product?: Product | null
  onClose: () => void
  onSave: () => void 
}) {
  const [formData, setFormData] = useState({
    name: product?.name || '',
    brand: product?.brand || '',
    category: product?.category || '',
    subcategory: product?.subcategory || '',
    description: product?.description || '',
    price: product?.price || 0,
    currency: product?.currency || 'USD',
    image_url: product?.image_url || '',
    affiliate_url: product?.affiliate_url || '',
    custom_description: product?.custom_description || '',
    recommendation_priority: product?.recommendation_priority || 5,
    style_tags: product?.style_tags?.join(', ') || '',
    color_tags: product?.color_tags?.join(', ') || '',
    season_tags: product?.season_tags?.join(', ') || ''
  })

  const [saving, setSaving] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setSaving(true)
    
    try {
      // First create/update the product
      const productPayload = {
        name: formData.name,
        brand: formData.brand,
        category: formData.category,
        subcategory: formData.subcategory,
        description: formData.description,
        price: formData.price,
        currency: formData.currency,
        image_url: formData.image_url,
        style_tags: formData.style_tags.split(',').map(t => t.trim()).filter(Boolean),
        color_tags: formData.color_tags.split(',').map(t => t.trim()).filter(Boolean),
        season_tags: formData.season_tags.split(',').map(t => t.trim()).filter(Boolean)
      }

      let productId = product?.id
      if (!productId) {
        // Create new product
        const productResponse = await fetch('/api/admin/products', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(productPayload)
        })
        const productData = await productResponse.json()
        if (productData.success) {
          productId = productData.data.id
        } else {
          throw new Error('Failed to create product')
        }
      } else {
        // Update existing product
        await fetch(`/api/admin/products/${productId}`, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(productPayload)
        })
      }

      // Then create/update the influencer-product relationship
      const influencerProductPayload = {
        influencer_id: influencerId,
        product_id: productId,
        affiliate_url: formData.affiliate_url,
        custom_description: formData.custom_description,
        recommendation_priority: formData.recommendation_priority
      }

      const ipResponse = await fetch('/api/admin/influencer-products', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(influencerProductPayload)
      })

      if (ipResponse.ok) {
        onSave()
      } else {
        throw new Error('Failed to link product to influencer')
      }

    } catch (error) {
      console.error('Error saving product:', error)
      alert('Error saving product')
    } finally {
      setSaving(false)
    }
  }

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
      <Card className="max-w-3xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <CardHeader>
          <CardTitle>{product ? 'Edit Product' : 'Add New Product'}</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Basic Product Info */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium">Product Name</label>
                <Input
                  required
                  value={formData.name}
                  onChange={(e) => setFormData({...formData, name: e.target.value})}
                  placeholder="Vintage Denim Jacket"
                />
              </div>
              <div>
                <label className="text-sm font-medium">Brand</label>
                <Input
                  required
                  value={formData.brand}
                  onChange={(e) => setFormData({...formData, brand: e.target.value})}
                  placeholder="Levi's"
                />
              </div>
            </div>

            <div className="grid grid-cols-3 gap-4">
              <div>
                <label className="text-sm font-medium">Category</label>
                <select
                  className="w-full p-2 border rounded"
                  value={formData.category}
                  onChange={(e) => setFormData({...formData, category: e.target.value})}
                  required
                >
                  <option value="">Select category</option>
                  <option value="Clothing">Clothing</option>
                  <option value="Shoes">Shoes</option>
                  <option value="Accessories">Accessories</option>
                  <option value="Beauty">Beauty</option>
                  <option value="Home">Home</option>
                </select>
              </div>
              <div>
                <label className="text-sm font-medium">Price</label>
                <Input
                  type="number"
                  step="0.01"
                  required
                  value={formData.price}
                  onChange={(e) => setFormData({...formData, price: parseFloat(e.target.value)})}
                />
              </div>
              <div>
                <label className="text-sm font-medium">Currency</label>
                <select
                  className="w-full p-2 border rounded"
                  value={formData.currency}
                  onChange={(e) => setFormData({...formData, currency: e.target.value})}
                >
                  <option value="USD">USD</option>
                  <option value="EUR">EUR</option>
                  <option value="GBP">GBP</option>
                </select>
              </div>
            </div>

            <div>
              <label className="text-sm font-medium">Product Description</label>
              <textarea
                className="w-full p-2 border rounded"
                rows={3}
                value={formData.description}
                onChange={(e) => setFormData({...formData, description: e.target.value})}
                placeholder="Classic vintage-wash denim jacket..."
              />
            </div>

            <div>
              <label className="text-sm font-medium">Product Image URL</label>
              <Input
                value={formData.image_url}
                onChange={(e) => setFormData({...formData, image_url: e.target.value})}
                placeholder="https://example.com/image.jpg"
              />
            </div>

            {/* Influencer-Specific Fields */}
            <div className="border-t pt-6">
              <h3 className="font-semibold mb-4">Influencer Settings</h3>
              
              <div>
                <label className="text-sm font-medium">Affiliate Link</label>
                <Input
                  required
                  value={formData.affiliate_url}
                  onChange={(e) => setFormData({...formData, affiliate_url: e.target.value})}
                  placeholder="https://shopltk.com/explore/..."
                />
              </div>

              <div className="mt-4">
                <label className="text-sm font-medium">Custom Description</label>
                <textarea
                  className="w-full p-2 border rounded"
                  rows={2}
                  value={formData.custom_description}
                  onChange={(e) => setFormData({...formData, custom_description: e.target.value})}
                  placeholder="I'm obsessed with this jacket! Perfect for..."
                />
                <p className="text-xs text-muted-foreground">How you'd describe this product to your followers</p>
              </div>

              <div className="mt-4">
                <label className="text-sm font-medium">Recommendation Priority (1-10)</label>
                <Input
                  type="number"
                  min="1"
                  max="10"
                  value={formData.recommendation_priority}
                  onChange={(e) => setFormData({...formData, recommendation_priority: parseInt(e.target.value)})}
                />
                <p className="text-xs text-muted-foreground">Higher priority products show up first in recommendations</p>
              </div>
            </div>

            {/* Tags */}
            <div className="border-t pt-6">
              <h3 className="font-semibold mb-4">Product Tags</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="text-sm font-medium">Style Tags</label>
                  <Input
                    value={formData.style_tags}
                    onChange={(e) => setFormData({...formData, style_tags: e.target.value})}
                    placeholder="casual, vintage, chic"
                  />
                </div>
                <div>
                  <label className="text-sm font-medium">Color Tags</label>
                  <Input
                    value={formData.color_tags}
                    onChange={(e) => setFormData({...formData, color_tags: e.target.value})}
                    placeholder="blue, denim, indigo"
                  />
                </div>
                <div>
                  <label className="text-sm font-medium">Season Tags</label>
                  <Input
                    value={formData.season_tags}
                    onChange={(e) => setFormData({...formData, season_tags: e.target.value})}
                    placeholder="spring, fall, year-round"
                  />
                </div>
              </div>
              <p className="text-xs text-muted-foreground mt-2">Separate multiple tags with commas</p>
            </div>

            <div className="flex justify-end space-x-2 pt-6">
              <Button type="button" variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button type="submit" disabled={saving}>
                {saving ? 'Saving...' : (product ? 'Update Product' : 'Add Product')}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}

// Component for influencer settings
function InfluencerSettingsForm({ 
  influencer, 
  onUpdate 
}: { 
  influencer: Influencer
  onUpdate: (influencer: Influencer) => void 
}) {
  const [formData, setFormData] = useState({
    name: influencer.name,
    username: influencer.username,
    bio: influencer.bio || '',
    specialty: influencer.specialty,
    followers: influencer.followers,
    categories: influencer.categories?.join(', ') || '',
    style_keywords: influencer.style_keywords?.join(', ') || '',
    social_links: {
      instagram: influencer.social_links?.instagram || '',
      tiktok: influencer.social_links?.tiktok || '',
      youtube: influencer.social_links?.youtube || ''
    }
  })

  const [saving, setSaving] = useState(false)

  const handleSave = async () => {
    setSaving(true)
    try {
      const payload = {
        ...formData,
        categories: formData.categories.split(',').map(c => c.trim()).filter(Boolean),
        style_keywords: formData.style_keywords.split(',').map(k => k.trim()).filter(Boolean)
      }

      const response = await fetch(`/api/admin/influencers/${influencer.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload)
      })

      if (response.ok) {
        const data = await response.json()
        onUpdate(data.data)
      }
    } catch (error) {
      console.error('Error updating influencer:', error)
    } finally {
      setSaving(false)
    }
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Profile Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium">Name</label>
              <Input
                value={formData.name}
                onChange={(e) => setFormData({...formData, name: e.target.value})}
              />
            </div>
            <div>
              <label className="text-sm font-medium">Username</label>
              <Input
                value={formData.username}
                onChange={(e) => setFormData({...formData, username: e.target.value})}
              />
            </div>
          </div>

          <div>
            <label className="text-sm font-medium">Bio</label>
            <textarea
              className="w-full p-2 border rounded"
              rows={3}
              value={formData.bio}
              onChange={(e) => setFormData({...formData, bio: e.target.value})}
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium">Specialty</label>
              <Input
                value={formData.specialty}
                onChange={(e) => setFormData({...formData, specialty: e.target.value})}
                placeholder="Fashion & Lifestyle"
              />
            </div>
            <div>
              <label className="text-sm font-medium">Followers</label>
              <Input
                value={formData.followers}
                onChange={(e) => setFormData({...formData, followers: e.target.value})}
                placeholder="5.2M"
              />
            </div>
          </div>

          <div>
            <label className="text-sm font-medium">Categories</label>
            <Input
              value={formData.categories}
              onChange={(e) => setFormData({...formData, categories: e.target.value})}
              placeholder="Fashion, Beauty, Lifestyle"
            />
          </div>

          <div>
            <label className="text-sm font-medium">Style Keywords</label>
            <Input
              value={formData.style_keywords}
              onChange={(e) => setFormData({...formData, style_keywords: e.target.value})}
              placeholder="trendy, chic, casual, elegant"
            />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Social Media Links</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <label className="text-sm font-medium">Instagram</label>
            <Input
              value={formData.social_links.instagram}
              onChange={(e) => setFormData({
                ...formData, 
                social_links: {...formData.social_links, instagram: e.target.value}
              })}
              placeholder="https://instagram.com/username"
            />
          </div>
          <div>
            <label className="text-sm font-medium">TikTok</label>
            <Input
              value={formData.social_links.tiktok}
              onChange={(e) => setFormData({
                ...formData, 
                social_links: {...formData.social_links, tiktok: e.target.value}
              })}
              placeholder="https://tiktok.com/@username"
            />
          </div>
          <div>
            <label className="text-sm font-medium">YouTube</label>
            <Input
              value={formData.social_links.youtube}
              onChange={(e) => setFormData({
                ...formData, 
                social_links: {...formData.social_links, youtube: e.target.value}
              })}
              placeholder="https://youtube.com/@username"
            />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Profile Images</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <ImageUpload
              currentImage={influencer.avatar}
              onImageChange={(url) => {
                // Handle avatar update
                fetch(`/api/admin/influencers/${influencer.id}`, {
                  method: 'PUT',
                  headers: { 'Content-Type': 'application/json' },
                  body: JSON.stringify({ avatar: url })
                })
              }}
              type="avatar"
              influencerId={influencer.id}
              label="Profile Avatar"
            />
            
            <ImageUpload
              currentImage={influencer.cover_image}
              onImageChange={(url) => {
                // Handle cover update
                fetch(`/api/admin/influencers/${influencer.id}`, {
                  method: 'PUT',
                  headers: { 'Content-Type': 'application/json' },
                  body: JSON.stringify({ cover_image: url })
                })
              }}
              type="cover"
              influencerId={influencer.id}
              label="Cover Image"
            />
          </div>
        </CardContent>
      </Card>

      <div className="flex justify-end">
        <Button onClick={handleSave} disabled={saving}>
          {saving ? 'Saving...' : 'Save Changes'}
        </Button>
      </div>
    </div>
  )
}