// src/app/layout.tsx
import './globals.css'
import { GeistSans } from 'geist/font/sans'
import { GeistMono } from 'geist/font/mono'
import { Metadata } from 'next'
import { Providers } from '@/components/providers'
import { Analytics } from '@vercel/analytics/react'

export const metadata: Metadata = {
  title: {
    default: 'ATE Shop - AI-Powered Influencer Shopping',
    template: '%s | ATE Shop'
  },
  description: 'Shop with your favorite influencers using AI-powered recommendations. Discover curated fashion, beauty, and lifestyle products.',
  keywords: ['influencer shopping', 'AI recommendations', 'fashion', 'beauty', 'lifestyle'],
  authors: [{ name: 'ATE Shop Team' }],
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: 'https://ate-shop.com',
    title: 'ATE Shop - AI-Powered Influencer Shopping',
    description: 'Shop with your favorite influencers using AI-powered recommendations.',
    siteName: 'ATE Shop',
    images: ['/og-image.png'],
  },
  robots: {
    index: true,
    follow: true,
  },
  icons: {
    icon: '/favicon.ico',
    apple: '/apple-touch-icon.png',
  },
}

interface RootLayoutProps {
  children: React.ReactNode
}

export default function RootLayout({ children }: RootLayoutProps) {
  return (
    <html 
      lang="en" 
      className={`${GeistSans.variable} ${GeistMono.variable}`}
      suppressHydrationWarning
    >
      <body className="min-h-screen bg-background font-sans antialiased">
        <Providers>
          {children}
        </Providers>
        <Analytics />
      </body>
    </html>
  )
}