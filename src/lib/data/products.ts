import { Product } from '../../types'

export const sampleProducts: Record<string, Product[]> = {
  beach: [
    {
      id: 'flowy-maxi-dress',
      name: 'Flowy Maxi Dress',
      description: 'Perfect for beach days with its lightweight fabric and flattering silhouette',
      price: 89,
      currency: 'USD',
      brand: 'Reformation',
      category: 'Dresses',
      image: 'https://images.unsplash.com/photo-1595777457583-95e059d581b8?w=300&h=300&fit=crop',
      affiliateUrl: 'https://example.com/product1',
      inStock: true,
      rating: 4.8,
      reviewCount: 124,
    },
    {
      id: 'linen-beach-coverup',
      name: 'Linen Beach Cover-Up',
      description: 'Effortless linen cover-up that transitions from beach to brunch',
      price: 45,
      currency: 'USD',
      brand: 'Madewell',
      category: 'Cover-Ups',
      image: 'https://images.unsplash.com/photo-1571513722275-4b3abf2e8b2b?w=300&h=300&fit=crop',
      affiliateUrl: 'https://example.com/product2',
      inStock: true,
      rating: 4.6,
      reviewCount: 89,
    }
  ],
  
  casual: [
    {
      id: 'vintage-denim-jacket',
      name: 'Vintage Denim Jacket',
      description: 'Classic denim jacket with vintage wash for that effortless cool-girl vibe',
      price: 78,
      currency: 'USD',
      brand: "Levi's",
      category: 'Jackets',
      image: 'https://images.unsplash.com/photo-1551028719-00167b16eac5?w=300&h=300&fit=crop',
      affiliateUrl: 'https://example.com/product3',
      inStock: true,
      rating: 4.7,
      reviewCount: 203,
    }
  ],
  
  luxury: [
    {
      id: 'designer-handbag',
      name: 'Designer Leather Handbag',
      description: 'Timeless luxury handbag crafted from premium Italian leather',
      price: 1250,
      currency: 'USD',
      brand: 'Bottega Veneta',
      category: 'Handbags',
      image: 'https://images.unsplash.com/photo-1584917865442-de89df76afd3?w=300&h=300&fit=crop',
      affiliateUrl: 'https://example.com/product4',
      inStock: true,
      rating: 4.9,
      reviewCount: 45,
    }
  ]
}

export const getAllProducts = (): Product[] => {
  return Object.values(sampleProducts).flat()
}

export const getProductsByCategory = (category: string): Product[] => {
  return sampleProducts[category] || []
}