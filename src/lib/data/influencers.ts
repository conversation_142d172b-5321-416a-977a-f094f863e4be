// src/lib/data/influencers.ts - Updated to fetch from database
import { Influencer } from '../../types'
import { supabase } from '../supabase/client'

// Remove the static data and replace with database calls
export async function getInfluencers(): Promise<Influencer[]> {
  try {
    const { data, error } = await supabase
      .from('influencers')
      .select('*')
      .eq('is_active', true)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Error fetching influencers:', error)
      return []
    }

    // Map database format to frontend format
    return data.map(influencer => ({
      id: influencer.id,
      name: influencer.name,
      username: influencer.username,
      avatar: influencer.avatar || `https://ui-avatars.com/api/?name=${encodeURIComponent(influencer.name)}&size=400`,
      coverImage: influencer.cover_image || 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=1200&h=600&fit=crop',
      bio: influencer.bio || '',
      specialty: influencer.specialty || '',
      followers: influencer.followers || '0',
      categories: influencer.categories || [],
      styleKeywords: influencer.style_keywords || [],
      socialLinks: influencer.social_links || {},
      verified: influencer.verified || false,
      rating: influencer.rating || 0,
      totalRecommendations: influencer.total_recommendations || 0,
      isActive: influencer.is_active || false,
    }))
  } catch (error) {
    console.error('Error in getInfluencers:', error)
    return []
  }
}

export async function getInfluencer(id: string): Promise<Influencer | null> {
  try {
    const { data, error } = await supabase
      .from('influencers')
      .select('*')
      .eq('id', id)
      .single()

    if (error) {
      console.error('Error fetching influencer:', error)
      return null
    }

    if (!data) return null

    // Map database format to frontend format
    return {
      id: data.id,
      name: data.name,
      username: data.username,
      avatar: data.avatar || `https://ui-avatars.com/api/?name=${encodeURIComponent(data.name)}&size=400`,
      coverImage: data.cover_image || 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=1200&h=600&fit=crop',
      bio: data.bio || '',
      specialty: data.specialty || '',
      followers: data.followers || '0',
      categories: data.categories || [],
      styleKeywords: data.style_keywords || [],
      socialLinks: data.social_links || {},
      verified: data.verified || false,
      rating: data.rating || 0,
      totalRecommendations: data.total_recommendations || 0,
      isActive: data.is_active || false,
    }
  } catch (error) {
    console.error('Error in getInfluencer:', error)
    return null
  }
}