// src/lib/supabase/client.ts
import { createClient } from '@supabase/supabase-js'

// Public client for frontend operations (uses ANON key - safe for frontend)
export const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
)


// Database types (auto-generated from Supabase)
export interface Database {
  public: {
    Tables: {
      influencers: {
        Row: {
          id: string
          name: string
          username: string
          avatar: string | null
          cover_image: string | null
          bio: string | null
          specialty: string | null
          followers: string | null
          categories: string[] | null
          style_keywords: string[] | null
          social_links: Json | null
          verified: boolean | null
          rating: number | null
          total_recommendations: number | null
          is_active: boolean | null
          ai_model_id: string | null
          personality_prompt: string | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id: string
          name: string
          username: string
          avatar?: string | null
          cover_image?: string | null
          bio?: string | null
          specialty?: string | null
          followers?: string | null
          categories?: string[] | null
          style_keywords?: string[] | null
          social_links?: Json | null
          verified?: boolean | null
          rating?: number | null
          total_recommendations?: number | null
          is_active?: boolean | null
          ai_model_id?: string | null
          personality_prompt?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          name?: string
          username?: string
          avatar?: string | null
          cover_image?: string | null
          bio?: string | null
          specialty?: string | null
          followers?: string | null
          categories?: string[] | null
          style_keywords?: string[] | null
          social_links?: Json | null
          verified?: boolean | null
          rating?: number | null
          total_recommendations?: number | null
          is_active?: boolean | null
          ai_model_id?: string | null
          personality_prompt?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
      }
      products: {
        Row: {
          id: string
          name: string
          description: string | null
          price: number
          currency: string | null
          brand: string | null
          category: string | null
          subcategory: string | null
          image_url: string | null
          base_affiliate_url: string | null
          in_stock: boolean | null
          rating: number | null
          review_count: number | null
          labels: string[] | null
          style_tags: string[] | null
          color_tags: string[] | null
          season_tags: string[] | null
          price_range: string | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id: string
          name: string
          description?: string | null
          price: number
          currency?: string | null
          brand?: string | null
          category?: string | null
          subcategory?: string | null
          image_url?: string | null
          base_affiliate_url?: string | null
          in_stock?: boolean | null
          rating?: number | null
          review_count?: number | null
          labels?: string[] | null
          style_tags?: string[] | null
          color_tags?: string[] | null
          season_tags?: string[] | null
          price_range?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          price?: number
          currency?: string | null
          brand?: string | null
          category?: string | null
          subcategory?: string | null
          image_url?: string | null
          base_affiliate_url?: string | null
          in_stock?: boolean | null
          rating?: number | null
          review_count?: number | null
          labels?: string[] | null
          style_tags?: string[] | null
          color_tags?: string[] | null
          season_tags?: string[] | null
          price_range?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
      }
      influencer_products: {
        Row: {
          id: string
          influencer_id: string
          product_id: string
          affiliate_url: string
          commission_rate: number | null
          custom_description: string | null
          recommendation_priority: number | null
          is_active: boolean | null
          created_at: string | null
        }
        Insert: {
          id?: string
          influencer_id: string
          product_id: string
          affiliate_url: string
          commission_rate?: number | null
          custom_description?: string | null
          recommendation_priority?: number | null
          is_active?: boolean | null
          created_at?: string | null
        }
        Update: {
          id?: string
          influencer_id?: string
          product_id?: string
          affiliate_url?: string
          commission_rate?: number | null
          custom_description?: string | null
          recommendation_priority?: number | null
          is_active?: boolean | null
          created_at?: string | null
        }
      }
      ai_training_data: {
        Row: {
          id: string
          influencer_id: string
          category: string
          user_input: string
          ai_response: string
          context_tags: string[] | null
          is_active: boolean | null
          created_at: string | null
        }
        Insert: {
          id?: string
          influencer_id: string
          category: string
          user_input: string
          ai_response: string
          context_tags?: string[] | null
          is_active?: boolean | null
          created_at?: string | null
        }
        Update: {
          id?: string
          influencer_id?: string
          category?: string
          user_input?: string
          ai_response?: string
          context_tags?: string[] | null
          is_active?: boolean | null
          created_at?: string | null
        }
      }
      chat_sessions: {
        Row: {
          id: string
          session_id: string
          influencer_id: string | null
          user_message: string
          ai_response: string
          products_recommended: string[] | null
          response_time_ms: number | null
          user_feedback: number | null
          created_at: string | null
        }
        Insert: {
          id?: string
          session_id: string
          influencer_id?: string | null
          user_message: string
          ai_response: string
          products_recommended?: string[] | null
          response_time_ms?: number | null
          user_feedback?: number | null
          created_at?: string | null
        }
        Update: {
          id?: string
          session_id?: string
          influencer_id?: string | null
          user_message?: string
          ai_response?: string
          products_recommended?: string[] | null
          response_time_ms?: number | null
          user_feedback?: number | null
          created_at?: string | null
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
  }
}

type Json = string | number | boolean | null | { [key: string]: Json | undefined } | Json[]

// Helper functions for common queries
export const influencerQueries = {
  getAll: () => supabase.from('influencers').select('*'),
  getById: (id: string) => supabase.from('influencers').select('*').eq('id', id).single(),
  getActive: () => supabase.from('influencers').select('*').eq('is_active', true)
}

export const productQueries = {
  getAll: () => supabase.from('products').select('*'),
  getById: (id: string) => supabase.from('products').select('*').eq('id', id).single(),
  getByCategory: (category: string) => supabase.from('products').select('*').eq('category', category),
  getInStock: () => supabase.from('products').select('*').eq('in_stock', true)
}

export const influencerProductQueries = {
  getByInfluencer: (influencerId: string) => 
    supabase
      .from('influencer_products')
      .select(`
        *,
        products (*)
      `)
      .eq('influencer_id', influencerId)
      .eq('is_active', true)
      .order('recommendation_priority', { ascending: false })
}