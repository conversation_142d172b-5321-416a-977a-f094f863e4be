// src/lib/services/simple-chat-service.ts - COMPLETE UPDATED FILE
import { ChatResponse, ProductRecommendation } from '../../types'
import { nanoid } from 'nanoid'
import OpenAI from 'openai'
import { supabaseAdmin } from '../supabase/server'
import { influencerPrompts } from '../prompts/influencer-prompts'

interface ProcessMessageParams {
  message: string
  influencerId: string
  conversationId?: string
  sessionId?: string
}

interface CombinedProduct {
  // Influencer product data
  affiliate_url: string
  custom_description: string | null
  recommendation_priority: number
  
  // Product data
  product_id: string
  product_name: string
  product_description: string | null
  product_price: number
  product_currency: string
  product_brand: string
  product_category: string
  product_image_url: string
  style_tags?: string[]
  color_tags?: string[]
  season_tags?: string[]
}

class SimpleChatService {
  private openai: OpenAI

  constructor() {
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    })
  }

  async processMessage({ 
    message, 
    influencerId, 
    conversationId, 
    sessionId = nanoid() 
  }: ProcessMessageParams): Promise<ChatResponse> {
    try {
      console.log('Processing message:', { message, influencerId })

      // Get products for this influencer
      const allProducts = await this.getInfluencerProducts(influencerId)
      console.log('Found products:', allProducts.length)

      // Filter by relevance to user message
      const relevantProducts = this.filterProductsByRelevance(message, allProducts)
      console.log('Relevant products:', relevantProducts.length)

      // Generate AI response
      const content = await this.generateAIResponse(influencerId, message, relevantProducts)

      // Create product recommendations
      const productRecommendations = this.createProductRecommendations(relevantProducts, influencerId)

      // Log for analytics
      await this.logChatSession({
        sessionId,
        influencerId,
        userMessage: message,
        aiResponse: content,
        productsRecommended: productRecommendations.map(p => p.product.id)
      })

      return {
        content,
        products: productRecommendations,
        confidence: 0.85,
        messageId: nanoid(),
      }

    } catch (error) {
      console.error('Chat service error:', error)
      return this.getFallbackResponse(influencerId)
    }
  }

  private async getInfluencerProducts(influencerId: string): Promise<CombinedProduct[]> {
    try {
      console.log('Fetching products for influencer:', influencerId)

      // Step 1: Get influencer products
      const { data: influencerProducts, error: ipError } = await supabaseAdmin
        .from('influencer_products')
        .select('*')
        .eq('influencer_id', influencerId)
        .eq('is_active', true)
        .order('recommendation_priority', { ascending: false })
        .limit(10)

      if (ipError) {
        console.error('Error fetching influencer products:', ipError)
        return []
      }

      if (!influencerProducts || influencerProducts.length === 0) {
        console.log('No influencer products found')
        return []
      }

      console.log('Found influencer products:', influencerProducts.length)

      // Step 2: Get product details for each
      const productIds = influencerProducts.map(ip => ip.product_id)
      
      const { data: products, error: productsError } = await supabaseAdmin
        .from('products')
        .select('*')
        .in('id', productIds)
        .eq('in_stock', true)

      if (productsError) {
        console.error('Error fetching products:', productsError)
        return []
      }

      console.log('Found products:', products?.length || 0)

      if (!products || products.length === 0) {
        return []
      }

      // Step 3: Combine the data safely
      const combinedData: CombinedProduct[] = influencerProducts
        .map(ip => {
          const product = products.find(p => p.id === ip.product_id)
          if (!product) return null

          return {
            // Influencer product data
            affiliate_url: ip.affiliate_url || '',
            custom_description: ip.custom_description,
            recommendation_priority: ip.recommendation_priority || 0,
            
            // Product data
            product_id: product.id,
            product_name: product.name || 'Unknown Product',
            product_description: product.description,
            product_price: product.price || 0,
            product_currency: product.currency || 'USD',
            product_brand: product.brand || 'Unknown Brand',
            product_category: product.category || 'Unknown',
            product_image_url: product.image_url || '',
            style_tags: product.style_tags || [],
            color_tags: product.color_tags || [],
            season_tags: product.season_tags || []
          }
        })
        .filter((item): item is CombinedProduct => item !== null)

      console.log('Successfully combined data:', combinedData.length)
      return combinedData

    } catch (error) {
      console.error('Error in getInfluencerProducts:', error)
      return []
    }
  }

  private filterProductsByRelevance(message: string, products: CombinedProduct[]): CombinedProduct[] {
    const lowerMessage = message.toLowerCase()
    
    // Extract keywords from user message
    const keywords = this.extractKeywords(lowerMessage)
    
    // SPECIFIC FILTERING FOR SWIMWEAR
    if (lowerMessage.includes('swim') || lowerMessage.includes('bikini') || lowerMessage.includes('bathing')) {
      const swimProducts = products.filter(product => {
        const searchText = [
          product.product_name,
          product.product_description || '',
          product.custom_description || '',
          ...(product.style_tags || []),
          ...(product.season_tags || [])
        ].join(' ').toLowerCase()
        
        return searchText.includes('bikini') || 
               searchText.includes('swim') || 
               searchText.includes('bathing') ||
               searchText.includes('beach')
      })
      
      if (swimProducts.length > 0) {
        return swimProducts.slice(0, 2) // Return max 2 swim products
      }
    }
    
    if (keywords.length === 0) {
      // No specific keywords, return top priority products
      return products.slice(0, 3)
    }

    // Find products that match keywords
    const keywordMatches = products.filter(product => {
      const searchText = [
        product.product_name,
        product.product_brand,
        product.product_category,
        product.product_description || '',
        product.custom_description || '',
        ...(product.style_tags || []),
        ...(product.color_tags || []),
        ...(product.season_tags || [])
      ].join(' ').toLowerCase()

      return keywords.some(keyword => searchText.includes(keyword))
    })

    // Return matches if found, otherwise top products
    return keywordMatches.length > 0 ? keywordMatches.slice(0, 3) : products.slice(0, 3)
  }

  private extractKeywords(message: string): string[] {
    const keywords: string[] = []

    // Define keyword categories with more specific matching
    const keywordCategories = {
      // Swimwear specific
      swimwear: ['swim', 'bikini', 'bathing', 'beach', 'pool', 'vacation', 'coverup', 'swimsuit'],
      
      // Clothing items
      clothing: ['dress', 'top', 'pants', 'skirt', 'jacket', 'shirt', 'blouse', 'sweater'],
      
      // Accessories
      accessories: ['bag', 'purse', 'necklace', 'earrings', 'jewelry', 'watch', 'belt'],
      
      // Shoes
      shoes: ['shoes', 'sandals', 'heels', 'boots', 'sneakers', 'flats'],
      
      // Occasions
      occasions: ['work', 'date', 'party', 'casual', 'formal', 'vacation', 'beach', 'night out', 'wedding'],
      
      // Styles
      styles: ['trendy', 'chic', 'boho', 'minimalist', 'vintage', 'elegant', 'cute', 'sexy', 'comfortable'],
      
      // Seasons/Weather
      seasonal: ['summer', 'winter', 'spring', 'fall', 'hot', 'cold', 'warm', 'cool']
    }

    // Extract keywords from all categories
    Object.values(keywordCategories).flat().forEach(keyword => {
      if (message.includes(keyword)) {
        keywords.push(keyword)
      }
    })

    return keywords
  }

  private async generateAIResponse(
    influencerId: string, 
    message: string, 
    products: CombinedProduct[]
  ): Promise<string> {
    const influencer = influencerPrompts[influencerId as keyof typeof influencerPrompts]
    
    if (!influencer || !this.openai || products.length === 0) {
      return this.getFallbackText(influencerId)
    }

    try {
      const prompt = this.buildPrompt(influencer, message, products)

      const response = await this.openai.chat.completions.create({
        model: "gpt-4o-mini",
        messages: [{ role: "user", content: prompt }],
        temperature: 0.8,
        max_tokens: 120,
        presence_penalty: 0.3
      })

      return response.choices[0]?.message?.content || this.getFallbackText(influencerId)

    } catch (error) {
      console.error('AI response error:', error)
      return this.getFallbackText(influencerId)
    }
  }

  private buildPrompt(influencer: any, message: string, products: CombinedProduct[]): string {
    return `${influencer.systemPrompt}

**USER REQUEST:** "${message}"

**AVAILABLE PRODUCTS TO RECOMMEND:**
${products.map((p, i) => 
  `${i+1}. ${p.product_name} by ${p.product_brand} - $${p.product_price}${p.custom_description ? ` (Your take: "${p.custom_description}")` : ''}`
).join('\n')}

**RESPONSE REQUIREMENTS:**
1. Stay in character as ${influencer.name}
2. Address their specific request (${message})
3. Only recommend products that are RELEVANT to their request
4. If asking for swimwear, ONLY recommend bikinis/swim products
5. Use your signature phrases and emojis
6. Keep response to 1-2 sentences max
7. Sound excited and helpful

Respond as ${influencer.name}:`
  }

  private createProductRecommendations(
    products: CombinedProduct[], 
    influencerId: string
  ): ProductRecommendation[] {
    return products.map(product => ({
      product: {
        id: product.product_id,
        name: product.product_name,
        description: product.custom_description || product.product_description || '',
        price: product.product_price,
        currency: product.product_currency,
        brand: product.product_brand,
        category: product.product_category,
        image: product.product_image_url,
        affiliateUrl: product.affiliate_url,
        inStock: true,
        rating: 4.5,
        reviewCount: 100
      },
      reason: this.getRecommendationReason(influencerId, product.product_name),
      confidence: 0.8 + (product.recommendation_priority / 100)
    }))
  }

  private getFallbackText(influencerId: string): string {
    const responses = {
      'alix-earle': "OMG babe! I have the PERFECT pieces for you! ✨",
      'emma-chamberlain': "Okay literally I found some pieces that would be so cute! ☕️",
      'sofia-martinez': "Hi gorgeous! I've found some amazing pieces that would be perfect for you! ✨",
      'style-maven': "Darling, I've curated some exquisite pieces for you 🖤"
    }

    return responses[influencerId as keyof typeof responses] || 
           "I'm so excited to show you some amazing pieces!"
  }

  private getRecommendationReason(influencerId: string, productName: string): string {
    // Use the custom description if available, otherwise use generic reasons
    const reasonTemplates = {
      'sofia-martinez': [
        "This piece is so versatile and will elevate any look!",
        "I'm obsessed with the quality and style of this!",
        "Perfect for creating that effortless chic vibe!",
        "This is such a good investment piece!"
      ],
      'alix-earle': [
        "OMG I'm literally obsessed with this piece!",
        "This is giving major vibes! You need it!",
        "Trust me on this one - it's everything!",
        "Perfect for that Miami girl aesthetic!"
      ],
      'emma-chamberlain': [
        "This is actually so cute and comfortable!",
        "I have this and wear it constantly! Love it",
        "Perfect for that effortless look you want",
        "Literally obsessed with this piece!"
      ],
      'style-maven': [
        "Exquisite choice that embodies sophistication 🖤",
        "The craftsmanship is impeccable, darling",
        "A timeless piece that elevates any wardrobe ✨"
      ]
    }

    const reasons = reasonTemplates[influencerId as keyof typeof reasonTemplates] || 
                   reasonTemplates['sofia-martinez']
    
    return reasons[Math.floor(Math.random() * reasons.length)]
  }

  private async logChatSession(data: {
    sessionId: string
    influencerId: string
    userMessage: string
    aiResponse: string
    productsRecommended: string[]
  }) {
    try {
      await supabaseAdmin
        .from('chat_sessions')
        .insert({
          session_id: data.sessionId,
          influencer_id: data.influencerId,
          user_message: data.userMessage,
          ai_response: data.aiResponse,
          products_recommended: data.productsRecommended,
          response_time_ms: 0
        })
    } catch (error) {
      console.error('Error logging chat session:', error)
    }
  }

  private getFallbackResponse(influencerId: string): ChatResponse {
    const fallbackResponses = {
      'alix-earle': "Hey babe! I'm so excited to help you find the perfect pieces! Let me show you some of my current faves! 💕✨",
      'emma-chamberlain': "Okay literally I want to help you find the cutest stuff! Let me pull up some options ☕️✨",
      'sofia-martinez': "Hi gorgeous! I'd love to help you find some amazing pieces! Let me show you my favorites! ✨",
      'style-maven': "Darling, I'd be delighted to show you some curated selections from my collection 🖤✨"
    }

    return {
      content: fallbackResponses[influencerId as keyof typeof fallbackResponses] || 
               "I'm excited to help you find some amazing pieces!",
      products: [],
      confidence: 0.6,
      messageId: nanoid()
    }
  }
}

export const simpleChatService = new SimpleChatService()