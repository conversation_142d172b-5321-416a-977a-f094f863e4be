import { create } from 'zustand'
import { ChatStore, Conversation, Message } from '../../types'
import { nanoid } from 'nanoid'

export const useChatStore = create<ChatStore>((set, get) => ({
  conversations: {},
  currentConversation: null,
  isLoading: false,

  setCurrentConversation: (conversation) => {
    set({ currentConversation: conversation })
  },

  addMessage: (conversationId, message) => {
    set((state) => {
      const conversation = state.conversations[conversationId]
      if (!conversation) return state

      const updatedConversation = {
        ...conversation,
        messages: [...conversation.messages, message],
        updatedAt: new Date()
      }

      return {
        conversations: {
          ...state.conversations,
          [conversationId]: updatedConversation
        },
        currentConversation: state.currentConversation?.id === conversationId 
          ? updatedConversation 
          : state.currentConversation
      }
    })
  },

  setLoading: (loading) => {
    set({ isLoading: loading })
  },

  createConversation: (influencerId) => {
    const conversation: Conversation = {
      id: nanoid(),
      influencerId,
      messages: [],
      createdAt: new Date(),
      updatedAt: new Date()
    }

    set((state) => ({
      conversations: {
        ...state.conversations,
        [conversation.id]: conversation
      }
    }))

    return conversation
  }
}))