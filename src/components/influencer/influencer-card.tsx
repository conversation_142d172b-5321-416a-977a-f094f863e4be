"use client"

import { memo } from "react"
import { MessageCircle, Users, Star, ExternalLink } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { Influencer } from "@/types"
import Link from "next/link"
import Image from "next/image"

interface InfluencerCardProps {
  influencer: Influencer
  onClick?: (influencer: Influencer) => void
}

export const InfluencerCard = memo(function InfluencerCard({ influencer, onClick }: InfluencerCardProps) {
  return (
    <Card className="group overflow-hidden cursor-pointer transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 will-change-transform w-full max-w-none">
      <div className={`h-24 sm:h-28 md:h-32 bg-gradient-to-r from-primary/20 to-purple-500/20 relative`}>
        <div className="absolute inset-0 bg-black/10" />
        <div className="absolute bottom-2 md:bottom-4 left-3 md:left-6">
          <div className="flex items-center space-x-2 text-foreground">
            <Users className="h-3 w-3 md:h-4 md:w-4" />
            <span className="text-xs md:text-sm font-medium">{influencer.followers} followers</span>
          </div>
        </div>
        {influencer.verified && (
          <div className="absolute top-2 md:top-4 right-2 md:right-4">
            <Badge variant="secondary" className="bg-white/20 text-foreground border-white/30 text-xs">
              ✓ Verified
            </Badge>
          </div>
        )}
      </div>
      
      <CardContent className="p-3 md:p-6">
        <div className="flex items-center space-x-3 md:space-x-4 mb-3 md:mb-4">
          <div className="relative flex-shrink-0">
            <Image
              src={influencer.avatar}
              alt={influencer.name}
              width={64}
              height={64}
              className="w-12 h-12 md:w-16 md:h-16 rounded-full border-2 md:border-4 border-background shadow-lg -mt-6 md:-mt-8 relative z-10"
            />
            {influencer.isActive && (
              <div className="absolute -bottom-0.5 -right-0.5 md:-bottom-1 md:-right-1 w-3 h-3 md:w-5 md:h-5 bg-green-500 rounded-full border-2 border-background" />
            )}
          </div>
          <div className="flex-1 min-w-0">
            <h3 className="text-base md:text-xl font-bold text-foreground group-hover:text-primary transition-colors truncate">
              {influencer.name}
            </h3>
            <p className="text-sm md:text-base text-primary font-medium truncate">{influencer.specialty}</p>
          </div>
          <div className="flex items-center gap-1 flex-shrink-0">
            <Star className="w-3 h-3 md:w-4 md:h-4 fill-yellow-400 text-yellow-400" />
            <span className="text-xs md:text-sm font-medium">{influencer.rating}</span>
          </div>
        </div>
        
        {/* Bio - Fixed width and proper wrapping */}
        <div className="mb-3 md:mb-4">
          <p className="text-muted-foreground text-xs md:text-sm leading-relaxed line-clamp-3 break-words">
            {influencer.bio}
          </p>
        </div>
        
        {/* Categories - Fixed width container */}
        <div className="mb-3 md:mb-4">
          <div className="flex flex-wrap gap-1 md:gap-2">
            {influencer.categories.slice(0, 3).map((category, idx) => (
              <Badge
                key={idx}
                variant="outline"
                className="text-xs flex-shrink-0"
              >
                {category}
              </Badge>
            ))}
            {influencer.categories.length > 3 && (
              <Badge variant="outline" className="text-xs flex-shrink-0">
                +{influencer.categories.length - 3}
              </Badge>
            )}
          </div>
        </div>
        
        {/* Footer info */}
        <div className="flex items-center justify-between text-xs md:text-sm text-muted-foreground mb-3 md:mb-4">
          <span className="truncate">{influencer.totalRecommendations} recommendations</span>
          <div className="flex items-center gap-1 md:gap-2 flex-shrink-0">
            {influencer.socialLinks.instagram && (
              <a href={influencer.socialLinks.instagram} target="_blank" rel="noopener noreferrer" className="hover:text-primary transition-colors">
                📸
              </a>
            )}
            {influencer.socialLinks.tiktok && (
              <a href={influencer.socialLinks.tiktok} target="_blank" rel="noopener noreferrer" className="hover:text-primary transition-colors">
                🎵
              </a>
            )}
            {influencer.socialLinks.youtube && (
              <a href={influencer.socialLinks.youtube} target="_blank" rel="noopener noreferrer" className="hover:text-primary transition-colors">
                📺
              </a>
            )}
          </div>
        </div>
        
        <Button 
          className="w-full group/btn text-sm md:text-base" 
          onClick={() => onClick?.(influencer)}
          asChild
        >
          <Link href={`/influencer/${influencer.id}`}>
            <MessageCircle className="mr-2 h-3 w-3 md:h-4 md:w-4" />
            Start Shopping
            <ExternalLink className="ml-2 h-3 w-3 md:h-4 md:w-4 opacity-0 group-hover/btn:opacity-100 transition-opacity" />
          </Link>
        </Button>
      </CardContent>
    </Card>
  )
})