"use client"

import { useState } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Star, Users, MessageCircle, ExternalLink, ChevronDown, ChevronUp } from "lucide-react"
import { Button } from "../ui/button"
import { Badge } from "../ui/badge"
import { Card, CardContent } from "../ui/card"
import { Influencer } from "../../types"
import Image from "next/image"

interface InfluencerProfileProps {
  influencer: Influencer
}

export function InfluencerProfile({ influencer }: InfluencerProfileProps) {
  const [isExpanded, setIsExpanded] = useState(false)
  return (
    <Card className="overflow-hidden">
      {/* Mobile Compact Header */}
      <div className="lg:hidden">
        <div className="p-4 border-b">
          <div className="flex items-center space-x-3">
            <div className="relative">
              <Image
                src={influencer.avatar}
                alt={influencer.name}
                width={48}
                height={48}
                className="w-12 h-12 rounded-full border-2 border-background shadow-md"
              />
              {influencer.isActive && (
                <div className="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-green-500 rounded-full border-2 border-background" />
              )}
            </div>
            
            <div className="flex-1 min-w-0">
              <h2 className="text-lg font-bold truncate">{influencer.name}</h2>
              <p className="text-sm text-primary font-medium truncate">{influencer.specialty}</p>
            </div>
            
            <div className="flex items-center gap-2">
              {influencer.verified && (
                <Badge variant="secondary" className="text-xs">
                  ✓
                </Badge>
              )}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsExpanded(!isExpanded)}
                className="p-2"
              >
                {isExpanded ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
              </Button>
            </div>
          </div>
        </div>

        {/* Mobile Expandable Content */}
        <AnimatePresence>
          {isExpanded && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: "auto", opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.3, ease: "easeInOut" }}
              className="overflow-hidden"
            >
              <CardContent className="p-4 space-y-4">
                {/* Bio */}
                <div>
                  <p className="text-sm leading-relaxed text-muted-foreground">
                    {influencer.bio}
                  </p>
                </div>
                
                {/* Social Links */}
                {(influencer.socialLinks.instagram || influencer.socialLinks.tiktok || influencer.socialLinks.youtube) && (
                  <div className="flex items-center gap-2">
                    {influencer.socialLinks.instagram && (
                      <a
                        href={influencer.socialLinks.instagram}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="p-2 bg-muted hover:bg-muted/80 rounded-full transition-colors"
                      >
                        <span className="text-sm">📸</span>
                      </a>
                    )}
                    {influencer.socialLinks.tiktok && (
                      <a
                        href={influencer.socialLinks.tiktok}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="p-2 bg-muted hover:bg-muted/80 rounded-full transition-colors"
                      >
                        <span className="text-sm">🎵</span>
                      </a>
                    )}
                    {influencer.socialLinks.youtube && (
                      <a
                        href={influencer.socialLinks.youtube}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="p-2 bg-muted hover:bg-muted/80 rounded-full transition-colors"
                      >
                        <span className="text-sm">📺</span>
                      </a>
                    )}
                  </div>
                )}
                
                {/* Categories */}
                <div>
                  <h4 className="text-sm font-semibold mb-2">Style Categories</h4>
                  <div className="flex flex-wrap gap-1">
                    {influencer.categories.slice(0, 4).map((category) => (
                      <Badge key={category} variant="secondary" className="text-xs">
                        {category}
                      </Badge>
                    ))}
                    {influencer.categories.length > 4 && (
                      <Badge variant="secondary" className="text-xs">
                        +{influencer.categories.length - 4}
                      </Badge>
                    )}
                  </div>
                </div>
                
                {/* Style Keywords */}
                <div>
                  <h4 className="text-sm font-semibold mb-2">Style Vibes</h4>
                  <div className="flex flex-wrap gap-1">
                    {influencer.styleKeywords.slice(0, 4).map((keyword) => (
                      <Badge key={keyword} variant="outline" className="text-xs">
                        {keyword}
                      </Badge>
                    ))}
                    {influencer.styleKeywords.length > 4 && (
                      <Badge variant="outline" className="text-xs">
                        +{influencer.styleKeywords.length - 4}
                      </Badge>
                    )}
                  </div>
                </div>
              </CardContent>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Desktop Full Profile */}
      <div className="hidden lg:block">
        {/* Cover Image */}
        <div className="h-48 bg-gradient-to-r from-primary/20 to-purple-500/20 relative">
          <Image
            src={influencer.coverImage}
            alt={`${influencer.name} cover`}
            fill
            className="object-cover"
          />
          <div className="absolute inset-0 bg-black/20" />
          
          {/* Floating Stats */}
          <div className="absolute top-4 right-4 flex gap-2">
            {influencer.verified && (
              <Badge className="bg-white/20 text-white border-white/30">
                ✓ Verified
              </Badge>
            )}
            <Badge className="bg-white/20 text-white border-white/30">
              ⭐ {influencer.rating}
            </Badge>
          </div>
        </div>
        
        <CardContent className="p-8">
          {/* Avatar and Basic Info - Always Centered */}
          <div className="flex flex-col items-center text-center space-y-4 mb-8">
            <div className="relative">
              <Image
                src={influencer.avatar}
                alt={influencer.name}
                width={120}
                height={120}
                className="w-30 h-30 rounded-full border-4 border-background shadow-xl -mt-16 relative z-10"
              />
              {influencer.isActive && (
                <div className="absolute -bottom-2 -right-2 w-6 h-6 bg-green-500 rounded-full border-4 border-background" />
              )}
            </div>

            <div className="space-y-2">
              <h1 className="text-3xl font-bold">{influencer.name}</h1>
              <p className="text-lg text-primary font-medium">{influencer.specialty}</p>
              <p className="text-muted-foreground">@{influencer.username}</p>
            </div>

            {/* Social Links */}
            <div className="flex items-center gap-3">
              {influencer.socialLinks.instagram && (
                <a
                  href={influencer.socialLinks.instagram}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="p-2 bg-muted hover:bg-muted/80 rounded-full transition-colors"
                >
                  <span className="text-lg">📸</span>
                </a>
              )}
              {influencer.socialLinks.tiktok && (
                <a
                  href={influencer.socialLinks.tiktok}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="p-2 bg-muted hover:bg-muted/80 rounded-full transition-colors"
                >
                  <span className="text-lg">🎵</span>
                </a>
              )}
              {influencer.socialLinks.youtube && (
                <a
                  href={influencer.socialLinks.youtube}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="p-2 bg-muted hover:bg-muted/80 rounded-full transition-colors"
                >
                  <span className="text-lg">📺</span>
                </a>
              )}
            </div>
          </div>

          {/* Content - Centered */}
          <div className="space-y-6 max-w-md mx-auto">
              {/* Bio */}
              <div>
                <p className="text-lg leading-relaxed text-muted-foreground">
                  {influencer.bio}
                </p>
              </div>
              
              {/* Categories */}
              <div>
                <h3 className="text-lg font-semibold mb-3">Style Categories</h3>
                <div className="flex flex-wrap gap-2">
                  {influencer.categories.map((category) => (
                    <Badge key={category} variant="secondary">
                      {category}
                    </Badge>
                  ))}
                </div>
              </div>
              
              {/* Style Keywords */}
              <div>
                <h3 className="text-lg font-semibold mb-3">Style Vibes</h3>
                <div className="flex flex-wrap gap-2">
                  {influencer.styleKeywords.map((keyword) => (
                    <Badge key={keyword} variant="outline">
                      {keyword}
                    </Badge>
                  ))}
                </div>
              </div>
          </div>
        </CardContent>
      </div>
    </Card>
  )
}