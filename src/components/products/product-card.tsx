"use client"

import { motion } from "framer-motion"
import { ExternalLink, Star, Heart, ShoppingBag } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { ProductCardProps } from "@/types"
import { formatPrice } from "@/lib/utils"
import Image from "next/image"

export function ProductCard({ product, recommendation, onProductClick }: ProductCardProps) {
  const handleProductClick = (e: React.MouseEvent) => {
    e.preventDefault() // Prevent default behavior
    e.stopPropagation() // Stop event bubbling
    
    // Call the onProductClick callback if provided
    onProductClick?.(product)
    
    // Open affiliate link in new tab (ONLY ONCE)
    if (product.affiliateUrl) {
      window.open(product.affiliateUrl, '_blank', 'noopener,noreferrer')
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.3 }}
      className="w-full"
    >
      <Card className="group overflow-hidden bg-card/80 backdrop-blur-sm border border-border/50">
        <CardContent className="p-2 md:p-3">
          <div className="flex gap-2 md:gap-3">
            {/* Product Image */}
            <div className="relative flex-shrink-0">
              <div className="w-16 h-16 md:w-20 md:h-20 rounded-lg overflow-hidden bg-muted">
                <Image
                  src={product.image}
                  alt={product.name}
                  width={80}
                  height={80}
                  className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
                />
              </div>
              
              {/* AI Pick Badge */}
              {recommendation && (
                <Badge className="absolute -top-1 -right-1 text-xs px-1 py-0.5 bg-primary/90 text-primary-foreground">
                  AI
                </Badge>
              )}
            </div>
            
            {/* Product Details */}
            <div className="flex-1 min-w-0 flex flex-col justify-between">
              <div className="space-y-1">
                <h4 className="font-medium text-xs md:text-sm line-clamp-2 group-hover:text-primary transition-colors leading-tight">
                  {product.name}
                </h4>
                <p className="text-xs text-muted-foreground font-medium truncate">{product.brand}</p>
                
                <div className="flex items-center gap-1 md:gap-2 flex-wrap">
                  <p className="font-bold text-primary text-xs md:text-sm">
                    {formatPrice(product.price, product.currency)}
                  </p>
                  
                  {product.rating && (
                    <div className="flex items-center gap-1">
                      <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                      <span className="text-xs text-muted-foreground">
                        {product.rating}
                      </span>
                    </div>
                  )}
                </div>
              </div>
              
              {/* Action Button */}
              <div className="flex items-center justify-between mt-2">
                <Button 
                  size="sm" 
                  className="h-6 md:h-7 px-2 md:px-3 text-xs font-medium"
                  onClick={handleProductClick}
                >
                  <ShoppingBag className="h-3 w-3 mr-1" />
                  <span className="hidden sm:inline">Shop Now</span>
                  <span className="sm:hidden">Shop</span>
                </Button>
                
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-6 w-6 md:h-7 md:w-7 opacity-60 hover:opacity-100 transition-opacity"
                  onClick={(e) => {
                    e.stopPropagation()
                    // Handle wishlist functionality
                  }}
                >
                  <Heart className="h-3 w-3" />
                </Button>
              </div>
            </div>
          </div>
          
          {/* Recommendation Reason */}
          {recommendation && (
            <div className="mt-2 md:mt-3 p-2 bg-primary/5 rounded-lg border border-primary/10">
              <p className="text-xs text-primary font-medium line-clamp-2">
                💡 {recommendation.reason}
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </motion.div>
  )
}