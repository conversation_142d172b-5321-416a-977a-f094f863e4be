//not in use
//depreciated
"use client"

import { useState, useEffect } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { <PERSON>R<PERSON>, <PERSON>rkles, Star, Users, TrendingUp } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import Link from "next/link"
import Image from "next/image"

const stats = [
  { icon: Users, label: "Influencers", value: "500+" },
  { icon: Star, label: "Rating", value: "4.9" },
  { icon: TrendingUp, label: "Products", value: "50K+" },
]

const featuredInfluencers = [
  {
    id: "alix-earle",
    name: "<PERSON><PERSON>",
    avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=80&h=80&fit=crop&crop=face",
    specialty: "Fashion & Lifestyle"
  },
  {
    id: "emma-chamberlain", 
    name: "<PERSON>",
    avatar: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=80&h=80&fit=crop&crop=face",
    specialty: "Casual Chic"
  },
  {
    id: "style-maven",
    name: "Style Maven",
    avatar: "https://images.unsplash.com/photo-1487412720507-e7ab37603c6f?w=80&h=80&fit=crop&crop=face",
    specialty: "Luxury Fashion"
  }
]

export function HeroSection() {
  const [currentInfluencer, setCurrentInfluencer] = useState(0)

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentInfluencer((prev) => (prev + 1) % featuredInfluencers.length)
    }, 3000)

    return () => clearInterval(timer)
  }, [])

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-background via-primary/5 to-purple-500/10">
      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-primary/20 rounded-full blur-3xl animate-pulse" />
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-purple-500/20 rounded-full blur-3xl animate-pulse" style={{ animationDelay: "2s" }} />
      </div>

      <div className="container mx-auto px-4 py-20 relative z-10">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Left Content */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            className="space-y-8"
          >
            {/* Badge */}
            <Badge variant="secondary" className="inline-flex items-center gap-2 mb-6 px-4 py-2 bg-primary/10 text-primary border-primary/20">
              <Sparkles className="w-4 h-4" />
              AI-Powered Shopping
            </Badge>

            {/* Main Heading */}
            <div className="space-y-4">
              <h1 className="text-5xl lg:text-6xl font-bold tracking-tight">
                Shop with{" "}
                <span className="bg-gradient-to-r from-primary to-purple-600 bg-clip-text text-transparent">
                  AI-Powered
                </span>
                <br />
                Influencer Style
              </h1>
              
              <p className="text-xl text-muted-foreground leading-relaxed max-w-xl">
                Get personalized recommendations from your favorite influencers. 
                Discover your perfect style effortlessly.
              </p>
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4">
              <Button size="xl" className="group" asChild>
                <Link href="#featured">
                  Start Shopping
                  <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
                </Link>
              </Button>
              
              <Button size="xl" variant="outline" asChild>
                <Link href="/how-it-works">
                  Learn More
                </Link>
              </Button>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-3 gap-6 pt-8">
              {stats.map((stat, index) => (
                <div key={stat.label} className="text-center">
                  <div className="flex items-center justify-center w-12 h-12 bg-primary/10 rounded-2xl mb-2 mx-auto">
                    <stat.icon className="w-6 h-6 text-primary" />
                  </div>
                  <div className="text-2xl font-bold text-foreground">{stat.value}</div>
                  <div className="text-sm text-muted-foreground">{stat.label}</div>
                </div>
              ))}
            </div>
          </motion.div>

          {/* Right Content - Influencer Showcase */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="relative flex justify-center"
          >
            {/* Main Card */}
            <div className="relative">
              <motion.div
                className="relative w-80 h-96 rounded-3xl bg-card border shadow-2xl overflow-hidden backdrop-blur-lg"
                whileHover={{ scale: 1.02 }}
                transition={{ duration: 0.3 }}
              >
                <div className="absolute inset-0 bg-gradient-to-br from-primary/20 to-purple-500/20" />
                
                <div className="relative z-10 p-8 h-full flex flex-col">
                  <div className="flex items-center justify-between mb-6">
                    <Badge className="bg-primary/20 text-primary border-primary/30">
                      AI Recommendation
                    </Badge>
                    <div className="flex items-center gap-1">
                      <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                      <span className="text-sm font-medium">4.9</span>
                    </div>
                  </div>

                  <AnimatePresence mode="wait">
                    <motion.div
                      key={currentInfluencer}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -20 }}
                      transition={{ duration: 0.5 }}
                      className="flex-1 flex flex-col items-center text-center"
                    >
                      <div className="relative mb-4">
                        <Image
                          src={featuredInfluencers[currentInfluencer].avatar}
                          alt={featuredInfluencers[currentInfluencer].name}
                          width={80}
                          height={80}
                          className="rounded-full border-4 border-primary/20"
                        />
                        <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-green-500 rounded-full border-2 border-white" />
                      </div>

                      <h3 className="text-xl font-bold mb-2">
                        {featuredInfluencers[currentInfluencer].name}
                      </h3>
                      <p className="text-muted-foreground mb-6">
                        {featuredInfluencers[currentInfluencer].specialty}
                      </p>

                      <div className="space-y-3 w-full">
                        <div className="bg-background/50 rounded-xl p-3 text-left">
                          <p className="text-sm text-muted-foreground mb-1">Latest recommendation:</p>
                          <p className="font-medium">"Perfect for a beach day! ✨"</p>
                        </div>
                        
                        <Button variant="outline" size="sm" className="w-full" asChild>
                          <Link href={`/influencer/${featuredInfluencers[currentInfluencer].id}`}>
                            Chat & Shop
                          </Link>
                        </Button>
                      </div>
                    </motion.div>
                  </AnimatePresence>
                </div>
              </motion.div>

              {/* Floating Elements */}
              <motion.div
                className="absolute -top-4 -left-4 w-8 h-8 bg-primary rounded-full shadow-lg"
                animate={{ y: [0, -10, 0] }}
                transition={{ duration: 2, repeat: Infinity }}
              />
              <motion.div
                className="absolute -bottom-4 -right-4 w-6 h-6 bg-purple-500 rounded-full shadow-lg"
                animate={{ y: [0, 10, 0] }}
                transition={{ duration: 2, repeat: Infinity, delay: 1 }}
              />
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}