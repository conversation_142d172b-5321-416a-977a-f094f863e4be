"use client"

import { useState, useEffect, useRef, useMemo, useCallback } from "react"
import { motion, useReducedMotion } from "framer-motion"
import { Search, ChevronLeft, ChevronRight, Sparkles, TrendingUp } from "lucide-react"
import { InfluencerCard } from "@/components/influencer/influencer-card"
import { getInfluencers } from "@/lib/data/influencers"
import { Influencer } from "@/types"
import { Button } from "../ui/button"
import { Input } from "../ui/input"
import { Badge } from "../ui/badge"
import Link from "next/link"

export function FeaturedInfluencers() {
  const [influencers, setInfluencers] = useState<Influencer[]>([])
  const [filteredInfluencers, setFilteredInfluencers] = useState<Influencer[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState("")
  const [currentIndex, setCurrentIndex] = useState(0)
  const scrollRef = useRef<HTMLDivElement>(null)

  // Performance: Respect user's motion preferences
  const shouldReduceMotion = useReducedMotion()

  // Performance: Optimized animation variants
  const cardVariants = useMemo(() => ({
    hidden: {
      opacity: 0,
      y: shouldReduceMotion ? 0 : 30,
      scale: shouldReduceMotion ? 1 : 0.95
    },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        duration: shouldReduceMotion ? 0.1 : 0.4,
        ease: "easeOut"
      }
    }
  }), [shouldReduceMotion])

  const headerVariants = useMemo(() => ({
    hidden: { opacity: 0, y: shouldReduceMotion ? 0 : 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: shouldReduceMotion ? 0.1 : 0.6,
        ease: "easeOut"
      }
    }
  }), [shouldReduceMotion])

  useEffect(() => {
    async function loadInfluencers() {
      try {
        const data = await getInfluencers()
        setInfluencers(data)
        setFilteredInfluencers(data)
      } catch (error) {
        console.error('Error loading influencers:', error)
      } finally {
        setLoading(false)
      }
    }

    loadInfluencers()
  }, [])

  // Performance: Memoized filter function
  const filterInfluencers = useCallback((query: string, influencerList: Influencer[]) => {
    if (!query.trim()) {
      return influencerList
    }

    const lowerQuery = query.toLowerCase()
    return influencerList.filter(influencer =>
      influencer.name.toLowerCase().includes(lowerQuery) ||
      influencer.specialty.toLowerCase().includes(lowerQuery) ||
      influencer.categories.some(cat =>
        cat.toLowerCase().includes(lowerQuery)
      ) ||
      influencer.styleKeywords.some(keyword =>
        keyword.toLowerCase().includes(lowerQuery)
      )
    )
  }, [])

  // Filter influencers based on search query
  useEffect(() => {
    const filtered = filterInfluencers(searchQuery, influencers)
    setFilteredInfluencers(filtered)
  }, [searchQuery, influencers, filterInfluencers])

  // Performance: Memoized scroll functions
  const scrollToCard = useCallback((index: number) => {
    const container = scrollRef.current
    if (!container) return

    // Mobile responsive card width calculation
    const isMobile = window.innerWidth < 768
    const cardWidth = isMobile ? 280 + 16 : 320 + 32 // card width + gap
    const containerWidth = container.clientWidth
    const scrollPosition = index * cardWidth - (containerWidth / 2) + (cardWidth / 2)

    container.scrollTo({
      left: Math.max(0, scrollPosition),
      behavior: 'smooth'
    })
    setCurrentIndex(index)
  }, [])

  const nextCard = useCallback(() => {
    const nextIndex = (currentIndex + 1) % filteredInfluencers.length
    scrollToCard(nextIndex)
  }, [currentIndex, filteredInfluencers.length, scrollToCard])

  const prevCard = useCallback(() => {
    const prevIndex = currentIndex === 0 ? filteredInfluencers.length - 1 : currentIndex - 1
    scrollToCard(prevIndex)
  }, [currentIndex, filteredInfluencers.length, scrollToCard])

  if (loading) {
    return (
      <div className="space-y-8 md:space-y-16 px-4">
        <div className="text-center space-y-6 md:space-y-8">
          <motion.div
            variants={headerVariants}
            initial="hidden"
            animate="visible"
            className="space-y-4 md:space-y-6"
          >
            <div className="h-6 md:h-8 bg-gradient-to-r from-muted via-muted/50 to-muted rounded-lg w-48 mx-auto animate-pulse" />
            <div className="h-12 md:h-16 bg-gradient-to-r from-muted via-muted/50 to-muted rounded-lg w-full max-w-md mx-auto animate-pulse" />
            <div className="h-4 md:h-6 bg-gradient-to-r from-muted via-muted/50 to-muted rounded-lg w-80 mx-auto animate-pulse" />
          </motion.div>

          <div className="max-w-2xl mx-auto">
            <div className="h-12 md:h-14 bg-gradient-to-r from-muted via-muted/50 to-muted rounded-2xl animate-pulse" />
          </div>
        </div>

        <div className="flex gap-4 md:gap-8 overflow-x-auto px-4">
          {[1, 2, 3].map((i) => (
            <motion.div
              key={i}
              variants={cardVariants}
              initial="hidden"
              animate="visible"
              transition={{ delay: shouldReduceMotion ? 0 : i * 0.05 }}
              className="flex-shrink-0 w-70 md:w-80"
            >
              <div className="bg-card rounded-3xl p-4 md:p-6 space-y-4 shadow-xl">
                <div className="h-24 md:h-32 bg-gradient-to-r from-muted via-muted/50 to-muted rounded-2xl animate-pulse" />
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 md:w-16 md:h-16 bg-gradient-to-r from-muted via-muted/50 to-muted rounded-full animate-pulse" />
                  <div className="space-y-2 flex-1">
                    <div className="h-4 bg-gradient-to-r from-muted via-muted/50 to-muted rounded-lg w-24 animate-pulse" />
                    <div className="h-3 bg-gradient-to-r from-muted via-muted/30 to-muted rounded-lg w-16 animate-pulse" />
                  </div>
                </div>
                <div className="space-y-3">
                  <div className="h-3 bg-gradient-to-r from-muted via-muted/30 to-muted rounded-lg w-full animate-pulse" />
                  <div className="h-3 bg-gradient-to-r from-muted via-muted/30 to-muted rounded-lg w-3/4 animate-pulse" />
                </div>
                <div className="h-8 md:h-10 bg-gradient-to-r from-muted via-muted/50 to-muted rounded-2xl animate-pulse" />
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="relative overflow-hidden">
      <div className="relative z-10 space-y-8 md:space-y-16">
        {/* Header Section */}
        <div className="text-center space-y-6 md:space-y-8 px-4">
          <motion.div
            variants={headerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-50px" }}
            className="space-y-4 md:space-y-6"
          >
            <div className="flex items-center justify-center gap-2 mb-4">
              <Badge variant="secondary" className="px-3 md:px-4 py-2 bg-primary/10 text-primary border-primary/20">
                <Sparkles className="w-4 h-4 mr-2" />
                AI-Powered Shopping
              </Badge>
            </div>

            <h1 className="text-3xl sm:text-4xl md:text-6xl lg:text-7xl font-bold tracking-tight">
              Shop with{" "}
              <span className="relative">
                <span className="bg-gradient-to-r from-primary via-purple-600 to-blue-600 bg-clip-text text-transparent">
                  Top Influencers
                </span>
                <motion.div
                  className="absolute -bottom-1 md:-bottom-2 left-0 right-0 h-1 bg-gradient-to-r from-primary/30 via-purple-600/30 to-blue-600/30 rounded-full"
                  initial={{ scaleX: 0 }}
                  whileInView={{ scaleX: shouldReduceMotion ? 1 : 1 }}
                  transition={{
                    duration: shouldReduceMotion ? 0.1 : 0.8,
                    delay: shouldReduceMotion ? 0 : 0.3,
                    ease: "easeOut"
                  }}
                  viewport={{ once: true }}
                />
              </span>
            </h1>
            
            <p className="text-lg md:text-xl lg:text-2xl text-muted-foreground max-w-3xl mx-auto leading-relaxed px-4">
              Get personalized style recommendations from verified influencers using advanced AI technology
            </p>
          </motion.div>

          {/* Search Bar */}
          <motion.div
            variants={headerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-50px" }}
            transition={{ delay: shouldReduceMotion ? 0 : 0.2 }}
            className="max-w-2xl mx-auto px-4"
          >
            <div className="relative group">
              <Search className="absolute left-4 md:left-6 top-1/2 transform -translate-y-1/2 text-muted-foreground/60 h-4 w-4 md:h-5 md:w-5 transition-colors group-focus-within:text-primary" />
              <Input
                placeholder="Search by name, style, or category..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-12 md:pl-14 pr-4 md:pr-6 py-3 md:py-4 text-base md:text-lg bg-background/80 backdrop-blur-sm border-2 border-border/50 focus:border-primary/50 rounded-2xl shadow-lg transition-all duration-300 hover:shadow-xl focus:shadow-xl"
              />
              <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-primary/5 to-purple-500/5 opacity-0 group-focus-within:opacity-100 transition-opacity duration-300 pointer-events-none" />
            </div>
            
            {/* Search Stats */}
            <div className="flex items-center justify-center gap-4 md:gap-6 mt-4 text-sm text-muted-foreground">
              <div className="flex items-center gap-2">
                <TrendingUp className="w-4 h-4" />
                <span>{filteredInfluencers.length} Influencers</span>
              </div>
              <div className="w-1 h-1 bg-muted-foreground/30 rounded-full" />
              <span className="hidden sm:inline">AI-Powered Recommendations</span>
              <span className="sm:hidden">AI Recommendations</span>
              <div className="w-1 h-1 bg-muted-foreground/30 rounded-full hidden sm:block" />
              <span className="hidden sm:inline">Verified Profiles</span>
            </div>
          </motion.div>
        </div>

        {/* Influencer Carousel */}
        {filteredInfluencers.length > 0 ? (
          <motion.div
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: shouldReduceMotion ? 0.1 : 0.5 }}
            viewport={{ once: true, margin: "-100px" }}
            className="relative"
          >
            {/* Navigation Buttons - Hidden on mobile */}
            <div className="absolute left-2 md:left-4 top-1/2 -translate-y-1/2 z-20 hidden md:block">
              <Button
                variant="outline"
                size="icon"
                onClick={prevCard}
                className="h-10 w-10 md:h-12 md:w-12 rounded-full bg-background/80 backdrop-blur-sm border-2 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110"
              >
                <ChevronLeft className="h-4 w-4 md:h-5 md:w-5" />
              </Button>
            </div>
            
            <div className="absolute right-2 md:right-4 top-1/2 -translate-y-1/2 z-20 hidden md:block">
              <Button
                variant="outline"
                size="icon"
                onClick={nextCard}
                className="h-10 w-10 md:h-12 md:w-12 rounded-full bg-background/80 backdrop-blur-sm border-2 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110"
              >
                <ChevronRight className="h-4 w-4 md:h-5 md:w-5" />
              </Button>
            </div>

            {/* Carousel Container */}
            <div
              ref={scrollRef}
              className="flex gap-4 md:gap-8 overflow-x-auto scrollbar-hide px-4 md:px-8 py-8"
              style={{
                scrollBehavior: 'smooth',
                scrollSnapType: 'x mandatory',
                // Performance: Enable hardware acceleration
                willChange: 'scroll-position',
                transform: 'translateZ(0)'
              }}
            >
              {filteredInfluencers.map((influencer, index) => (
                <motion.div
                  key={influencer.id}
                  className="flex-shrink-0 w-72 sm:w-80 md:w-96 flex items-center"
                  style={{
                    scrollSnapAlign: 'center',
                    // Performance: Enable hardware acceleration for cards
                    willChange: 'transform',
                    transform: 'translateZ(0)'
                  }}
                  variants={cardVariants}
                  initial="hidden"
                  whileInView="visible"
                  viewport={{ once: true, margin: "-50px" }}
                  transition={{
                    delay: shouldReduceMotion ? 0 : Math.min(index * 0.05, 0.3) // Cap delay
                  }}
                >
                  <div className="relative group w-full">
                    {/* Optimized Card - Removed expensive effects */}
                    <div className="relative transform transition-transform duration-300 hover:scale-[1.02] will-change-transform">
                      {/* Simplified shadow effect instead of blur */}
                      <div className="absolute -inset-0.5 bg-gradient-to-r from-primary/10 via-purple-500/10 to-blue-500/10 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

                      {/* Card Content - Natural height */}
                      <div className="relative bg-card border border-border/50 rounded-3xl overflow-hidden shadow-lg group-hover:shadow-xl transition-shadow duration-300">
                        <InfluencerCard influencer={influencer} />
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>

            {/* Carousel Indicators */}
            <div className="flex justify-center gap-2 mt-4 md:mt-8">
              {filteredInfluencers.slice(0, 5).map((_, index) => (
                <button
                  key={index}
                  onClick={() => scrollToCard(index)}
                  className={`w-2 h-2 md:w-3 md:h-3 rounded-full transition-all duration-200 will-change-transform ${
                    index === currentIndex % 5
                      ? 'bg-primary scale-110'
                      : 'bg-muted-foreground/30 hover:bg-muted-foreground/50'
                  }`}
                />
              ))}
            </div>
          </motion.div>
        ) : (
          <NoResultsState searchQuery={searchQuery} />
        )}
      </div>

      {/* Custom Scrollbar Styles */}
      <style jsx>{`
        .scrollbar-hide {
          -ms-overflow-style: none;
          scrollbar-width: none;
        }
        .scrollbar-hide::-webkit-scrollbar {
          display: none;
        }
      `}</style>
    </div>
  )
}

// No Results State Component
function NoResultsState({ searchQuery }: { searchQuery: string }) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="text-center py-16 px-4"
    >
      <div className="w-20 h-20 md:w-24 md:h-24 bg-muted/30 rounded-full flex items-center justify-center mx-auto mb-6">
        <Search className="w-10 h-10 md:w-12 md:h-12 text-muted-foreground/50" />
      </div>
      <h3 className="text-lg md:text-xl font-semibold mb-2">No influencers found</h3>
      <p className="text-muted-foreground mb-6 px-4">
        {searchQuery ? (
          <>No results for "<span className="font-medium">{searchQuery}</span>". Try a different search term.</>
        ) : (
          "No influencers available. Add some in the admin dashboard!"
        )}
      </p>
      <Button variant="outline" asChild>
        <Link href="/admin">
          Add Influencers
        </Link>
      </Button>
    </motion.div>
  )
}