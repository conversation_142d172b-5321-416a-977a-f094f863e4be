"use client"

interface TypingIndicatorProps {
  influencer: { avatar: string; name: string }
}

export function TypingIndicator({ influencer }: TypingIndicatorProps) {
  return (
    <div className="flex justify-start">
      <div className="flex items-center space-x-2">
        <img
          src={influencer.avatar}
          alt={influencer.name}
          className="w-6 h-6 rounded-full"
        />
        <div className="bg-muted rounded-2xl px-4 py-2 mr-4">
          <div className="flex space-x-1">
            <div className="w-2 h-2 bg-muted-foreground/40 rounded-full animate-pulse" />
            <div 
              className="w-2 h-2 bg-muted-foreground/40 rounded-full animate-pulse" 
              style={{ animationDelay: '0.2s' }}
            />
            <div 
              className="w-2 h-2 bg-muted-foreground/40 rounded-full animate-pulse" 
              style={{ animationDelay: '0.4s' }}
            />
          </div>
        </div>
      </div>
    </div>
  )
}