"use client"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import { ExternalLink, Heart, ChevronLeft, ChevronRight } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Influencer } from "@/types"
import Image from "next/image"

interface Product {
  id: string
  name: string
  brand: string
  price: number
  currency: string
  image_url: string
  affiliate_url: string
  custom_description: string
  recommendation_priority: number
}

interface InfluencerFavoritesProps {
  influencer: Influencer
}

export function InfluencerFavorites({ influencer }: InfluencerFavoritesProps) {
  const [favorites, setFavorites] = useState<Product[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadFavorites()
  }, [influencer.id])

  const loadFavorites = async () => {
    try {
      const response = await fetch(`/api/admin/influencer-products?influencer_id=${influencer.id}`)
      const data = await response.json()
      
      if (data.success) {
        // Get top 8 products sorted by recommendation priority
        const topProducts = data.data
          .sort((a: any, b: any) => b.recommendation_priority - a.recommendation_priority)
          .slice(0, 8)
          .map((item: any) => ({
            id: item.products.id,
            name: item.products.name,
            brand: item.products.brand,
            price: item.products.price,
            currency: item.products.currency,
            image_url: item.products.image_url,
            affiliate_url: item.affiliate_url,
            custom_description: item.custom_description,
            recommendation_priority: item.recommendation_priority
          }))
        
        setFavorites(topProducts)
      }
    } catch (error) {
      console.error('Error loading favorites:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleProductClick = (product: Product) => {
    // Track click and open affiliate link
    if (product.affiliate_url) {
      window.open(product.affiliate_url, '_blank', 'noopener,noreferrer')
    }
  }

  const formatPrice = (price: number, currency: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency,
    }).format(price)
  }

  if (loading) {
    return (
      <div className="border-t bg-muted/30 p-3 md:p-6">
        <div className="space-y-4">
          <div className="h-6 bg-muted/50 rounded w-48 animate-pulse" />
          <div className="flex gap-3 md:gap-4 overflow-hidden">
            {[1, 2, 3, 4].map((i) => (
              <div key={i} className="flex-shrink-0 w-40 md:w-64">
                <div className="bg-card rounded-2xl p-3 md:p-4 space-y-3 animate-pulse">
                  <div className="h-24 md:h-32 bg-muted/50 rounded-xl" />
                  <div className="space-y-2">
                    <div className="h-4 bg-muted/50 rounded w-3/4" />
                    <div className="h-3 bg-muted/30 rounded w-1/2" />
                    <div className="h-5 bg-muted/50 rounded w-1/3" />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  if (favorites.length === 0) {
    return null
  }

  return (
    <div className="border-t bg-muted/30">
      <div className="p-3 md:p-6">
        <div className="flex items-center justify-between mb-3 md:mb-4">
          <div className="flex items-center gap-2 md:gap-3 min-w-0">
            <Image
              src={influencer.avatar}
              alt={influencer.name}
              width={32}
              height={32}
              className="w-6 h-6 md:w-8 md:h-8 rounded-full flex-shrink-0"
            />
            <h3 className="font-semibold text-sm md:text-lg truncate">
              {influencer.name}'s Favorites
            </h3>
            <Badge variant="secondary" className="text-xs flex-shrink-0">
              {favorites.length}
            </Badge>
          </div>
        </div>

        {/* Products Scroll - Mobile optimized */}
        <div className="relative">
          <div
            className="flex gap-3 md:gap-4 overflow-x-auto scrollbar-hide pb-2"
            style={{ scrollSnapType: 'x mandatory' }}
          >
            {favorites.map((product, index) => (
              <motion.div
                key={product.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                className="flex-shrink-0 w-40 sm:w-48 md:w-64"
                style={{ scrollSnapAlign: 'start' }}
              >
                <Card 
                  className="group cursor-pointer transition-all duration-200 hover:shadow-lg hover:-translate-y-1"
                  onClick={() => handleProductClick(product)}
                >
                  <CardContent className="p-2 md:p-4">
                    {/* Product Image */}
                    <div className="relative mb-2 md:mb-3 overflow-hidden rounded-xl">
                      <Image
                        src={product.image_url}
                        alt={product.name}
                        width={240}
                        height={200}
                        className="w-full h-24 sm:h-28 md:h-32 object-cover transition-transform duration-300 group-hover:scale-105"
                      />
                      
                      {/* Overlay on hover */}
                      <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center">
                        <div className="bg-white/90 rounded-full p-2">
                          <ExternalLink className="h-3 w-3 md:h-4 md:w-4 text-gray-800" />
                        </div>
                      </div>

                      {/* Priority badge for top picks */}
                      {product.recommendation_priority >= 9 && (
                        <Badge className="absolute top-1 md:top-2 left-1 md:left-2 bg-primary/90 text-xs">
                          Top Pick
                        </Badge>
                      )}
                    </div>

                    {/* Product Info */}
                    <div className="space-y-1 md:space-y-2">
                      <div>
                        <h4 className="font-medium text-xs md:text-sm line-clamp-2 group-hover:text-primary transition-colors">
                          {product.name}
                        </h4>
                        <p className="text-xs text-muted-foreground truncate">{product.brand}</p>
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <p className="font-semibold text-primary text-xs md:text-sm">
                          {formatPrice(product.price, product.currency)}
                        </p>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-6 w-6 md:h-8 md:w-8 opacity-0 group-hover:opacity-100 transition-opacity"
                          onClick={(e) => {
                            e.stopPropagation()
                            // Handle wishlist functionality
                          }}
                        >
                          <Heart className="h-3 w-3 md:h-4 md:w-4" />
                        </Button>
                      </div>

                      {/* Custom description from influencer */}
                      {product.custom_description && (
                        <p className="text-xs text-muted-foreground line-clamp-2 italic">
                          "{product.custom_description.split('.')[0]}..."
                        </p>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>

        {/* Mobile scroll indicator */}
        <div className="mt-2 md:mt-3 text-center">
          <p className="text-xs text-muted-foreground">
            Swipe to see more →
          </p>
        </div>
      </div>

      {/* Custom scrollbar styles */}
      <style jsx>{`
        .scrollbar-hide {
          -ms-overflow-style: none;
          scrollbar-width: none;
        }
        .scrollbar-hide::-webkit-scrollbar {
          display: none;
        }
        .line-clamp-2 {
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }
      `}</style>
    </div>
  )
}