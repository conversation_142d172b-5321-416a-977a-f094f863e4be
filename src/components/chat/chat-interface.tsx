// src/components/chat/chat-interface.tsx - REPLACE ENTIRE FILE
"use client"

import { useState, useRef, useEffect } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { MessageBubble } from "./message-bubble"
import { ChatInput } from "./chat-input"
import { TypingIndicator } from "./typing-indicator"
import { InfluencerFavorites } from "./influencer-favorites"
import { useChatStore } from "../../lib/store/chat-store"
import { Influencer, Message } from "../../types"
import { nanoid } from "nanoid"

interface ChatInterfaceProps {
  influencer: Influencer
}

// Dynamic welcome messages based on influencer
const getWelcomeMessage = (influencer: Influencer): string => {
  const welcomeMessages: Record<string, string> = {
    'alix-earle': `Hey babe! 💕 It's Alix here! I'm so excited to help you find the perfect pieces for your wardrobe. What kind of look are you going for today?`,
    'sofia-martinez': `Hi gorgeous! ✨ <PERSON> here and I'm thrilled to help you elevate your style! What are you looking to shop for today?`,
    'emma-chamberlain': `Hey! Emma here ☕️ Literally so excited to help you find some amazing pieces! What vibe are we going for today?`,
    'style-maven': `Hello darling! 🖤 I'm here to help you curate the perfect wardrobe pieces. What can I help you find today?`
  }
  
  return welcomeMessages[influencer.id] || 
         `Hi! I'm ${influencer.name} and I'm excited to help you find amazing pieces! What are you looking for today? ✨`
}

export function ChatInterface({ influencer }: ChatInterfaceProps) {
  const { 
    conversations, 
    currentConversation, 
    isLoading, 
    setCurrentConversation, 
    addMessage, 
    setLoading,
    createConversation 
  } = useChatStore()
  
  const [isTyping, setIsTyping] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  
  // Initialize conversation
  useEffect(() => {
    const existingConversation = Object.values(conversations).find(
      conv => conv.influencerId === influencer.id
    )
    
    if (existingConversation) {
      setCurrentConversation(existingConversation)
    } else {
      const newConversation = createConversation(influencer.id)
      setCurrentConversation(newConversation)
      
      // Add dynamic welcome message based on influencer
      const welcomeMessage: Message = {
        id: nanoid(),
        senderId: influencer.id,
        senderType: 'ai',
        content: getWelcomeMessage(influencer),
        timestamp: new Date(),
        products: []
      }
      
      addMessage(newConversation.id, welcomeMessage)
    }
  }, [influencer.id, conversations, setCurrentConversation, createConversation, addMessage, influencer])
  
  // Auto scroll to bottom with better positioning
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ 
        behavior: "smooth",
        block: "end",
        inline: "nearest"
      })
    }
  }, [currentConversation?.messages, isTyping])
  
  const handleSendMessage = async (content: string) => {
    if (!currentConversation || !content.trim()) return
    
    // Add user message
    const userMessage: Message = {
      id: nanoid(),
      senderId: 'user',
      senderType: 'user',
      content: content.trim(),
      timestamp: new Date(),
      products: []
    }
    
    addMessage(currentConversation.id, userMessage)
    setIsTyping(true)
    setLoading(true)
    
    try {
      // Call API
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: content.trim(),
          influencerId: influencer.id,
          conversationId: currentConversation.id,
        }),
      })
      
      const data = await response.json()
      
      if (data.success) {
        // Add AI response
        const aiMessage: Message = {
          id: data.data.messageId,
          senderId: influencer.id,
          senderType: 'ai',
          content: data.data.content,
          timestamp: new Date(),
          products: data.data.products || []
        }
        
        // Simulate typing delay
        setTimeout(() => {
          addMessage(currentConversation.id, aiMessage)
          setIsTyping(false)
        }, 1000 + Math.random() * 1000)
      } else {
        throw new Error(data.error?.message || 'Failed to send message')
      }
    } catch (error) {
      console.error('Chat error:', error)
      
      // Add error message
      const errorMessage: Message = {
        id: nanoid(),
        senderId: influencer.id,
        senderType: 'ai',
        content: "Sorry babe, I'm having some technical difficulties! Try asking me again in a moment. 💕",
        timestamp: new Date(),
        products: []
      }
      
      setTimeout(() => {
        addMessage(currentConversation.id, errorMessage)
        setIsTyping(false)
      }, 1000)
    } finally {
      setLoading(false)
    }
  }
  
  return (
    <div className="flex flex-col bg-background w-full">
      {/* Chat Container - Mobile responsive height */}
      <div className="flex flex-col h-[70vh] md:h-[600px] max-h-screen">
        {/* Chat Header */}
        <div className="flex items-center gap-3 p-3 md:p-4 border-b bg-muted/30 flex-shrink-0">
          <div className="relative">
            <img
              src={influencer.avatar}
              alt={influencer.name}
              className="w-8 h-8 md:w-10 md:h-10 rounded-full"
            />
            <div className="absolute -bottom-0.5 -right-0.5 md:-bottom-1 md:-right-1 w-2.5 h-2.5 md:w-3 md:h-3 bg-green-500 rounded-full border-2 border-background" />
          </div>
          <div className="flex-1 min-w-0">
            <h3 className="font-semibold text-sm md:text-base truncate">{influencer.name}</h3>
            <p className="text-xs text-muted-foreground">Online now • Responds quickly</p>
          </div>
          <div className="text-right hidden sm:block">
            <div className="text-xs text-muted-foreground">AI-Powered</div>
            <div className="text-xs text-primary font-medium">Style Expert</div>
          </div>
        </div>
        
        {/* Messages - Scrollable area */}
        <div className="flex-1 overflow-y-auto p-3 md:p-4 space-y-4 min-h-0">
          <AnimatePresence>
            {currentConversation?.messages.map((message) => (
              <motion.div
                key={message.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
              >
                <MessageBubble
                  message={message}
                  influencer={influencer}
                  isOwn={message.senderType === 'user'}
                />
              </motion.div>
            ))}
          </AnimatePresence>
          
          {isTyping && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
            >
              <TypingIndicator influencer={influencer} />
            </motion.div>
          )}
          
          <div ref={messagesEndRef} className="h-4" />
        </div>
        
        {/* Chat Input - Fixed at bottom */}
        <div className="border-t bg-muted/30 flex-shrink-0">
          <ChatInput 
            onSendMessage={handleSendMessage}
            isLoading={isLoading}
            disabled={isTyping}
          />
        </div>
      </div>

      {/* Influencer Favorites Section - Below chat */}
      <InfluencerFavorites influencer={influencer} />
    </div>
  )
}