"use client"

import { useState } from "react"
import { Send, Loader2 } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { ChatInputProps } from "@/types"

export function ChatInput({ onSendMessage, isLoading, disabled }: ChatInputProps) {
  const [message, setMessage] = useState("")

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (!message.trim() || disabled) return
    
    onSendMessage(message.trim())
    setMessage("")
  }

  const suggestions = [
    "Date night outfit",
    "Summer vacation",
    "Work style",
    "Casual weekend",
    "Special event"
  ]

  return (
    <div className="p-3 md:p-4 space-y-3">
      {/* Quick Suggestions - More compact on mobile */}
      {!isLoading && (
        <div className="flex flex-wrap gap-1 md:gap-2">
          {suggestions.slice(0, 3).map((suggestion) => (
            <button
              key={suggestion}
              onClick={() => setMessage(suggestion)}
              className="px-2 md:px-3 py-1 text-xs bg-primary/10 text-primary rounded-full hover:bg-primary/20 transition-colors flex-shrink-0"
            >
              {suggestion}
            </button>
          ))}
          {/* Show more suggestions on larger screens */}
          <div className="hidden md:flex gap-2">
            {suggestions.slice(3).map((suggestion) => (
              <button
                key={suggestion}
                onClick={() => setMessage(suggestion)}
                className="px-3 py-1 text-xs bg-primary/10 text-primary rounded-full hover:bg-primary/20 transition-colors"
              >
                {suggestion}
              </button>
            ))}
          </div>
        </div>
      )}
      
      <form onSubmit={handleSubmit} className="flex items-center space-x-2">
        <div className="flex-1 relative">
          <Input
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            placeholder="Ask for style recommendations..."
            disabled={disabled}
            className="pr-10 md:pr-12 rounded-full text-sm"
          />
          <Button
            type="submit"
            size="icon"
            disabled={!message.trim() || disabled}
            className="absolute right-1 top-1/2 transform -translate-y-1/2 h-7 w-7 md:h-8 md:w-8 rounded-full"
          >
            {isLoading ? (
              <Loader2 className="h-3 w-3 md:h-4 md:w-4 animate-spin" />
            ) : (
              <Send className="h-3 w-3 md:h-4 md:w-4" />
            )}
          </Button>
        </div>
      </form>
      
      <div className="flex items-center justify-center">
        <div className="flex items-center space-x-2 text-xs text-muted-foreground">
          <span>✨</span>
          <span className="hidden sm:inline">Powered by AI • Personalized recommendations</span>
          <span className="sm:hidden">AI-Powered Style Expert</span>
        </div>
      </div>
    </div>
  )
}