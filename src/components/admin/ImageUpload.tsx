"use client"

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Upload, X, Image as ImageIcon, Loader2 } from 'lucide-react'

interface ImageUploadProps {
  currentImage?: string
  onImageChange: (url: string) => void
  type: 'avatar' | 'cover'
  influencerId: string
  label: string
}

export function ImageUpload({ 
  currentImage, 
  onImageChange, 
  type, 
  influencerId, 
  label 
}: ImageUploadProps) {
  const [uploading, setUploading] = useState(false)
  const [preview, setPreview] = useState<string | null>(currentImage || null)
  const [dragActive, setDragActive] = useState(false)

  const handleFileSelect = async (file: File) => {
    if (!file) return

    // Validate file
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
    if (!allowedTypes.includes(file.type)) {
      alert('Invalid file type. Only JPEG, PNG, and WebP are allowed.')
      return
    }

    if (file.size > 5 * 1024 * 1024) {
      alert('File too large. Maximum size is 5MB.')
      return
    }

    // Show preview immediately
    const reader = new FileReader()
    reader.onload = (e) => {
      setPreview(e.target?.result as string)
    }
    reader.readAsDataURL(file)

    // Upload file to Supabase
    setUploading(true)
    try {
      const formData = new FormData()
      formData.append('file', file)
      formData.append('type', type)
      formData.append('influencerId', influencerId)

      const response = await fetch('/api/admin/upload', {
        method: 'POST',
        body: formData,
      })

      const result = await response.json()

      if (result.success) {
        onImageChange(result.url)
        setPreview(result.url)
      } else {
        alert('Upload failed: ' + result.error)
        setPreview(currentImage || null)
      }
    } catch (error) {
      console.error('Upload error:', error)
      alert('Upload failed')
      setPreview(currentImage || null)
    } finally {
      setUploading(false)
    }
  }

  const handleFileInput = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) handleFileSelect(file)
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setDragActive(false)
    const file = e.dataTransfer.files?.[0]
    if (file) handleFileSelect(file)
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    setDragActive(true)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    setDragActive(false)
  }

  const handleRemove = () => {
    setPreview(null)
    onImageChange('')
  }

  return (
    <div className="space-y-4">
      <label className="text-sm font-medium">{label}</label>
      
      {/* Preview */}
      {preview ? (
        <div className="relative">
          <img 
            src={preview} 
            alt={`${type} preview`}
            className={`object-cover rounded-lg border ${
              type === 'avatar' 
                ? 'w-24 h-24 rounded-full' 
                : 'w-full h-32'
            }`}
          />
          {!uploading && (
            <Button
              type="button"
              variant="destructive"
              size="sm"
              className="absolute top-2 right-2"
              onClick={handleRemove}
            >
              <X className="h-4 w-4" />
            </Button>
          )}
          {uploading && (
            <div className="absolute inset-0 bg-black/50 rounded-lg flex items-center justify-center">
              <Loader2 className="h-6 w-6 text-white animate-spin" />
            </div>
          )}
        </div>
      ) : (
        <div 
          className={`border-2 border-dashed rounded-lg flex items-center justify-center transition-colors ${
            dragActive 
              ? 'border-primary bg-primary/5' 
              : 'border-muted-foreground/25'
          } ${
            type === 'avatar' ? 'w-24 h-24' : 'w-full h-32'
          }`}
          onDrop={handleDrop}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
        >
          {uploading ? (
            <Loader2 className="h-8 w-8 text-muted-foreground animate-spin" />
          ) : (
            <div className="text-center">
              <ImageIcon className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
              <p className="text-xs text-muted-foreground">Drop image here</p>
            </div>
          )}
        </div>
      )}

      {/* Upload Button */}
      <div className="flex items-center space-x-2">
        <Input
          type="file"
          accept="image/*"
          onChange={handleFileInput}
          disabled={uploading}
          className="hidden"
          id={`${type}-upload-${influencerId}`}
        />
        <Button
          type="button"
          variant="outline"
          size="sm"
          disabled={uploading}
          onClick={() => document.getElementById(`${type}-upload-${influencerId}`)?.click()}
        >
          <Upload className="h-4 w-4 mr-2" />
          {uploading ? 'Uploading...' : 'Upload'}
        </Button>
        
        {type === 'avatar' && (
          <p className="text-xs text-muted-foreground">
            400x400px, max 5MB
          </p>
        )}
        {type === 'cover' && (
          <p className="text-xs text-muted-foreground">
            1200x400px, max 5MB
          </p>
        )}
      </div>
    </div>
  )
}