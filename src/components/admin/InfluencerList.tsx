"use client"

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON> } from 'next/navigation'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { 
  Plus, Search, Settings, BarChart3, Users, 
  Eye, Edit, Play, Pause, TrendingUp, MessageCircle
} from 'lucide-react'

interface Influencer {
  id: string
  name: string
  username: string
  specialty: string
  followers: string
  verified: boolean
  is_active: boolean
  total_recommendations: number
  avatar?: string
  rating: number
}

export function InfluencerList() {
  const router = useRouter()
  const [influencers, setInfluencers] = useState<Influencer[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState("")

  useEffect(() => {
    loadInfluencers()
  }, [])

  const loadInfluencers = async () => {
    try {
      const response = await fetch('/api/admin/influencers')
      const data = await response.json()
      if (data.success) {
        setInfluencers(data.data)
      }
    } catch (error) {
      console.error('Error loading influencers:', error)
    } finally {
      setLoading(false)
    }
  }

  const filteredInfluencers = influencers.filter(influencer =>
    influencer.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    influencer.username.toLowerCase().includes(searchQuery.toLowerCase()) ||
    influencer.specialty.toLowerCase().includes(searchQuery.toLowerCase())
  )

  const handleToggleStatus = async (influencer: Influencer) => {
    try {
      const response = await fetch(`/api/admin/influencers/${influencer.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          is_active: !influencer.is_active
        })
      })

      if (response.ok) {
        setInfluencers(prev => 
          prev.map(inf => 
            inf.id === influencer.id 
              ? { ...inf, is_active: !inf.is_active }
              : inf
          )
        )
      }
    } catch (error) {
      console.error('Error toggling influencer status:', error)
    }
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="h-8 bg-muted rounded w-64 animate-pulse" />
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[1, 2, 3].map((i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="space-y-4">
                  <div className="flex items-center space-x-4">
                    <div className="w-16 h-16 bg-muted rounded-full" />
                    <div className="space-y-2">
                      <div className="h-4 bg-muted rounded w-24" />
                      <div className="h-3 bg-muted rounded w-16" />
                    </div>
                  </div>
                  <div className="h-8 bg-muted rounded" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold">Influencers</h1>
          <p className="text-muted-foreground">Manage your AI-powered influencer profiles</p>
        </div>
        <Button onClick={() => router.push('/admin/influencer/new')}>
          <Plus className="h-4 w-4 mr-2" />
          Add Influencer
        </Button>
      </div>

      {/* Search */}
      <div className="relative max-w-md">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
        <Input
          placeholder="Search influencers..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="pl-10"
        />
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Influencers</p>
                <p className="text-2xl font-bold">{influencers.length}</p>
              </div>
              <Users className="h-8 w-8 text-primary" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Active</p>
                <p className="text-2xl font-bold">{influencers.filter(i => i.is_active).length}</p>
              </div>
              <Play className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Followers</p>
                <p className="text-2xl font-bold">
                  {influencers.reduce((sum, inf) => {
                    const followers = parseFloat(inf.followers.replace(/[^\d.]/g, ''))
                    const multiplier = inf.followers.includes('M') ? 1000000 : 
                                     inf.followers.includes('K') ? 1000 : 1
                    return sum + (followers * multiplier)
                  }, 0) / 1000000}M
                </p>
              </div>
              <TrendingUp className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Avg Rating</p>
                <p className="text-2xl font-bold">
                  {influencers.length > 0 ? (influencers.reduce((sum, inf) => sum + inf.rating, 0) / influencers.length).toFixed(1) : '0.0'}
                </p>
              </div>
              <MessageCircle className="h-8 w-8 text-yellow-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Influencer Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredInfluencers.map((influencer) => (
          <Card key={influencer.id} className="hover:shadow-lg transition-shadow">
            <CardContent className="p-6">
              {/* Header */}
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <img
                    src={influencer.avatar || `https://ui-avatars.com/api/?name=${encodeURIComponent(influencer.name)}&size=64`}
                    alt={influencer.name}
                    className="w-12 h-12 rounded-full border-2 border-background shadow"
                  />
                  <div>
                    <h3 className="font-semibold">{influencer.name}</h3>
                    <p className="text-sm text-muted-foreground">@{influencer.username}</p>
                  </div>
                </div>
                <Badge variant={influencer.is_active ? "default" : "secondary"}>
                  {influencer.is_active ? "Live" : "Draft"}
                </Badge>
              </div>

              {/* Info */}
              <div className="space-y-2 mb-4">
                <p className="text-sm">{influencer.specialty}</p>
                <div className="flex items-center justify-between text-sm text-muted-foreground">
                  <span>{influencer.followers} followers</span>
                  <span>⭐ {influencer.rating}</span>
                </div>
                {influencer.verified && (
                  <Badge variant="outline" className="text-xs">
                    ✓ Verified
                  </Badge>
                )}
              </div>

              {/* Actions */}
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => router.push(`/admin/influencer/${influencer.id}`)}
                  className="flex-1"
                >
                  <Settings className="h-4 w-4 mr-2" />
                  Manage
                </Button>
                
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => window.open(`/influencer/${influencer.id}`, '_blank')}
                >
                  <Eye className="h-4 w-4" />
                </Button>
                
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleToggleStatus(influencer)}
                >
                  {influencer.is_active ? (
                    <Pause className="h-4 w-4 text-orange-500" />
                  ) : (
                    <Play className="h-4 w-4 text-green-500" />
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Empty State */}
      {filteredInfluencers.length === 0 && (
        <Card>
          <CardContent className="p-12 text-center">
            <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">
              {searchQuery ? 'No influencers found' : 'No influencers yet'}
            </h3>
            <p className="text-muted-foreground mb-4">
              {searchQuery 
                ? `No results for "${searchQuery}". Try a different search term.`
                : 'Get started by adding your first influencer to the platform.'
              }
            </p>
            {!searchQuery && (
              <Button onClick={() => router.push('/admin/influencer/new')}>
                <Plus className="h-4 w-4 mr-2" />
                Add Your First Influencer
              </Button>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  )
}