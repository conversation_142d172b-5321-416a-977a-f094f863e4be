// src/types/index.ts

export interface Influencer {
    id: string
    name: string
    username: string
    avatar: string
    coverImage: string
    bio: string
    specialty: string
    followers: string
    categories: string[]
    styleKeywords: string[]
    socialLinks: {
      instagram?: string
      tiktok?: string
      youtube?: string
      twitter?: string
    }
    verified: boolean
    rating: number
    totalRecommendations: number
    isActive: boolean
  }
  
  export interface Product {
    id: string
    name: string
    description: string
    price: number
    currency: string
    brand: string
    category: string
    image: string
    affiliateUrl: string
    inStock: boolean
    rating: number
    reviewCount: number
  }
  
  export interface Message {
    id: string
    senderId: string
    senderType: 'user' | 'ai'
    content: string
    timestamp: Date
    products?: ProductRecommendation[]
  }
  
  export interface ProductRecommendation {
    product: Product
    reason: string
    confidence: number
  }
  
  export interface Conversation {
    id: string
    influencerId: string
    messages: Message[]
    createdAt: Date
    updatedAt: Date
  }
  
  export interface ChatResponse {
    content: string
    products: ProductRecommendation[]
    confidence: number
    messageId: string
  }
  
  // Component Props Types
  export interface InfluencerCardProps {
    influencer: Influencer
    onClick?: (influencer: Influencer) => void
  }
  
  export interface ProductCardProps {
    product: Product
    recommendation?: ProductRecommendation
    onProductClick?: (product: Product) => void
  }
  
  export interface MessageBubbleProps {
    message: Message
    influencer: Influencer
    isOwn: boolean
  }
  
  export interface ChatInputProps {
    onSendMessage: (content: string) => void
    isLoading?: boolean
    disabled?: boolean
  }
  
  // Store Types
  export interface ChatStore {
    conversations: Record<string, Conversation>
    currentConversation: Conversation | null
    isLoading: boolean
    
    // Actions
    setCurrentConversation: (conversation: Conversation) => void
    addMessage: (conversationId: string, message: Message) => void
    setLoading: (loading: boolean) => void
    createConversation: (influencerId: string) => Conversation
  }
  
  // Utility Types
  export type DeepPartial<T> = {
    [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P]
  }